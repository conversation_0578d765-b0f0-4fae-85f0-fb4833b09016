// netlify/functions/proxy.js

import { authenticate } from '../../src_netlify/utils/auth.js';
import { createErrorResponse, createCorsHeaders } from '../../src_netlify/utils/error.js';
import { handleModels } from '../../src_netlify/handlers/models.js';
import { handleCompletions } from '../../src_netlify/handlers/completions.js';
import { handleEmbeddings } from '../../src_netlify/handlers/embeddings.js';

export const handler = async (event, context) => {
  // --- OPTIONS Preflight Request ---
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 204,
      headers: {
        'Access-Control-Allow-Origin': '*', // This will be overridden by netlify.toml headers
        'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    };
  }

  try {
    // 1. Authenticate the request
    authenticate(event);
    
    // 2. Simple routing logic
    const path = event.path.replace('/.netlify/functions/proxy', '');

    if (path.endsWith('/models')) {
      if (event.httpMethod !== 'GET') {
        throw new HttpError(`Method ${event.httpMethod} not allowed for /models endpoint. Use GET.`, 405);
      }
      return await handleModels(event);
    }

    if (path.endsWith('/chat/completions')) {
      if (event.httpMethod !== 'POST') {
        throw new HttpError(`Method ${event.httpMethod} not allowed for /chat/completions endpoint. Use POST.`, 405);
      }
      return await handleCompletions(event);
    }

    if (path.endsWith('/embeddings')) {
      if (event.httpMethod !== 'POST') {
        throw new HttpError(`Method ${event.httpMethod} not allowed for /embeddings endpoint. Use POST.`, 405);
      }
      return await handleEmbeddings(event);
    }

    // 3. If no route matches, return 404
    return {
      statusCode: 404,
      headers: {
        'Content-Type': 'application/json',
        ...createCorsHeaders(),
      },
      body: JSON.stringify({
        error: {
          message: 'Not Found',
          type: 'invalid_request_error',
          code: 404
        }
      })
    };

  } catch (err) {
    // 4. Catch any errors and return a safe response
    return createErrorResponse(err);
  }
};