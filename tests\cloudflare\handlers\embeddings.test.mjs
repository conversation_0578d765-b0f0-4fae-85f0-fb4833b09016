// tests/cloudflare/handlers/embeddings.test.mjs

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { handleEmbeddings } from '../../../cloudflare/handlers/embeddings.mjs';
import * as transformers from '../../../cloudflare/transformers/embeddings.mjs';

describe('handleEmbeddings handler', () => {
  let mockEnv;

  beforeEach(() => { vi.clearAllMocks(); });
  afterEach(() => { vi.restoreAllMocks(); });

  beforeEach(() => {
    mockEnv = {
      GEMINI_API_BASE_URL: 'https://api.gemini.mock',
      GEMINI_API_VERSION: 'v1beta',
      GEMINI_API_KEY: 'test-key',
    };
  });

  it('should handle a successful embedding request', async () => {
    const openAIRequest = { model: 'text-embedding-004', input: 'test' };
    const mockGeminiResponse = { embeddings: [{ values: [0.1] }] };
    
    // Spy on our transformers
    const reqSpy = vi.spyOn(transformers, 'transformToGeminiEmbeddingsRequest').mockReturnValue({
      geminiRequest: { requests: [] },
      model: 'models/text-embedding-004',
    });
    const resSpy = vi.spyOn(transformers, 'transformToOpenAIEmbeddingsResponse').mockReturnValue({
      object: 'list', data: [],
    });

    const fetchSpy = vi.spyOn(global, 'fetch').mockResolvedValue(new Response(JSON.stringify(mockGeminiResponse)));
    
    const request = new Request('https://test.com/embeddings', { method: 'POST', body: JSON.stringify(openAIRequest) });
    await handleEmbeddings(request, mockEnv);

    expect(reqSpy).toHaveBeenCalledWith(openAIRequest);
    expect(fetchSpy).toHaveBeenCalledWith(
      'https://api.gemini.mock/v1beta/models/text-embedding-004:batchEmbedContents',
      expect.anything()
    );
    expect(resSpy).toHaveBeenCalledWith(mockGeminiResponse, openAIRequest.model);
  });
});