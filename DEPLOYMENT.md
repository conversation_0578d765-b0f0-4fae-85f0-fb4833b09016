# Deployment Guide

This guide provides detailed instructions for deploying the Gemini/OpenAI API Proxy to all three supported platforms.

## Platform Comparison

| Feature | Cloudflare Workers | Vercel Edge Functions | Netlify Functions |
|---------|-------------------|----------------------|-------------------|
| **Runtime** | V8 Isolates | Edge Runtime | Node.js |
| **Cold Start** | ~5ms | ~10-50ms | ~100-500ms |
| **Caching** | KV Storage | In-memory | In-memory |
| **Global Edge** | ✅ 200+ locations | ✅ 100+ locations | ✅ Global CDN |
| **Streaming** | ✅ Full support | ✅ Full support | ❌ Not supported |
| **Free Tier** | 100k requests/day | 100GB-hours/month | 125k requests/month |

## Quick Start

### 1. Cloudflare Workers (Recommended for Performance)

**Prerequisites:**
- Cloudflare account
- Wrangler CLI installed (`npm install -g wrangler`)

**Steps:**
```bash
# 1. Login to Cloudflare
wrangler login

# 2. Create KV namespace for caching
wrangler kv:namespace create "CACHE"

# 3. Update wrangler.toml with the KV namespace ID
# Copy the ID from the previous command output

# 4. Set secrets
wrangler secret put GEMINI_API_KEY
wrangler secret put PROXY_API_KEYS

# 5. Deploy
npm run deploy:cf
```

**Configuration:**
- Update `wrangler.toml` with your preferred worker name
- Set `CORS_ALLOWED_ORIGINS` to your frontend URL
- The KV namespace provides persistent caching across requests

### 2. Vercel Edge Functions (Recommended for Simplicity)

**Prerequisites:**
- Vercel account
- Vercel CLI installed (`npm install -g vercel`)

**Steps:**
```bash
# 1. Login to Vercel
vercel login

# 2. Initialize project (first time only)
vercel

# 3. Set environment variables
vercel env add GEMINI_API_KEY
vercel env add PROXY_API_KEYS

# 4. Deploy
npm run deploy:vercel
```

**Configuration:**
- Update `vercel.json` CORS settings for your frontend
- Environment variables are managed through Vercel dashboard
- Uses in-memory caching (resets on cold starts)

### 3. Netlify Functions (Recommended for Git-based Workflows)

**Prerequisites:**
- Netlify account
- Git repository connected to Netlify

**Steps:**
```bash
# 1. Connect your Git repository to Netlify
# 2. Set environment variables in Netlify dashboard:
#    - GEMINI_API_KEY
#    - PROXY_API_KEYS

# 3. Update netlify.toml CORS headers
# 4. Push to your Git repository - Netlify will auto-deploy
```

**Configuration:**
- Netlify automatically deploys on Git push
- Environment variables set in Netlify dashboard
- Uses in-memory caching with node-cache
- **Note**: Streaming is not supported on Netlify Functions. Streaming requests will return a 400 error with instructions to use Cloudflare or Vercel.

## Environment Variables

### Required Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `GEMINI_API_KEY` | Your Google Gemini API key | `AIza...` |
| `PROXY_API_KEYS` | Comma-separated API keys for client auth | `key1,key2,key3` |

### Optional Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `CORS_ALLOWED_ORIGINS` | Allowed CORS origins | `*` |
| `GEMINI_API_BASE_URL` | Gemini API base URL | `https://generativelanguage.googleapis.com` |
| `GEMINI_API_VERSION` | Gemini API version | `v1beta` |

## Testing Your Deployment

After deployment, test your proxy with:

```bash
# Test models endpoint
curl -H "Authorization: Bearer your-proxy-api-key" \
     https://your-deployment-url/models

# Test chat completions
curl -X POST \
     -H "Authorization: Bearer your-proxy-api-key" \
     -H "Content-Type: application/json" \
     -d '{"model":"gemini-pro","messages":[{"role":"user","content":"Hello!"}]}' \
     https://your-deployment-url/chat/completions
```

## Monitoring and Logs

### Cloudflare Workers
- View logs: `wrangler tail`
- Analytics: Cloudflare Dashboard > Workers & Pages

### Vercel
- View logs: Vercel Dashboard > Functions tab
- Real-time logs: `vercel logs --follow`

### Netlify
- View logs: Netlify Dashboard > Functions tab
- Real-time logs: `netlify functions:log`

## Troubleshooting

### Common Issues

1. **CORS Errors**: Update CORS settings in platform configuration
2. **Authentication Failures**: Verify `PROXY_API_KEYS` environment variable
3. **Gemini API Errors**: Check `GEMINI_API_KEY` and API quotas
4. **Cold Start Timeouts**: Consider using Cloudflare Workers for fastest cold starts

### Performance Optimization

1. **Cloudflare**: Use KV caching for models endpoint
2. **Vercel**: Consider upgrading to Pro for better performance
3. **Netlify**: Use background functions for non-critical operations

## Security Considerations

1. **API Key Rotation**: Regularly rotate your `PROXY_API_KEYS`
2. **CORS Configuration**: Set specific origins instead of `*` in production
3. **Rate Limiting**: Consider implementing rate limiting for production use
4. **Monitoring**: Set up alerts for unusual traffic patterns
