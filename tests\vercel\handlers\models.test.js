// tests/vercel/handlers/models.test.js

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { handleModels } from '../../../vercel/handlers/models.js';

// Mock the dependencies
vi.mock('../../../vercel/utils/error.js', () => ({
  HttpError: class HttpError extends Error {
    constructor(message, status) {
      super(message);
      this.status = status;
    }
  }
}));

vi.mock('../../../vercel/utils/auth.js', () => ({
  createGeminiHeaders: vi.fn(() => new Headers({ 'Content-Type': 'application/json' }))
}));

vi.mock('../../../vercel/transformers/models.js', () => ({
  transformToOpenAIModels: vi.fn((data) => ({
    object: 'list',
    data: data.models.map(model => ({ id: model.name, object: 'model' }))
  }))
}));

vi.mock('../../../vercel/utils/cache.js', () => ({
  getCache: vi.fn(),
  setCache: vi.fn()
}));

// Mock global fetch
global.fetch = vi.fn();

import { createGeminiHeaders } from '../../../vercel/utils/auth.js';
import { transformToOpenAIModels } from '../../../vercel/transformers/models.js';
import { getCache, setCache } from '../../../vercel/utils/cache.js';

describe('Vercel Models Handler', () => {
  const mockEnv = {
    GEMINI_API_BASE_URL: 'https://generativelanguage.googleapis.com',
    GEMINI_API_VERSION: 'v1beta'
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return cached data when available', async () => {
    const cachedData = { object: 'list', data: [] };
    getCache.mockReturnValue(cachedData);

    const request = new Request('https://test.com/models');
    const response = await handleModels(request, mockEnv);
    
    expect(response.status).toBe(200);
    expect(getCache).toHaveBeenCalledWith('models_list');
    expect(fetch).not.toHaveBeenCalled();
  });

  it('should fetch from upstream when cache is empty', async () => {
    getCache.mockReturnValue(null);
    
    const mockGeminiResponse = {
      models: [
        { name: 'models/gemini-pro', displayName: 'Gemini Pro', description: 'Test model' }
      ]
    };
    
    fetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockGeminiResponse)
    });

    const request = new Request('https://test.com/models');
    const response = await handleModels(request, mockEnv);
    
    expect(response.status).toBe(200);
    expect(fetch).toHaveBeenCalledWith(
      'https://generativelanguage.googleapis.com/v1beta/models',
      { headers: expect.any(Headers) }
    );
    expect(transformToOpenAIModels).toHaveBeenCalledWith(mockGeminiResponse);
    expect(setCache).toHaveBeenCalledWith('models_list', expect.any(Object), 3600000);
  });

  it('should handle upstream API errors', async () => {
    getCache.mockReturnValue(null);
    
    fetch.mockResolvedValue({
      ok: false,
      status: 500
    });

    const request = new Request('https://test.com/models');
    
    await expect(handleModels(request, mockEnv)).rejects.toThrow();
  });
});
