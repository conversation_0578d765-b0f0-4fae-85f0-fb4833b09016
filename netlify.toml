# netlify.toml

# This [build] block contains the instructions for Netlify's build system.
[build]
  # This is your quality gate. Before deploying, Netlify will run this command.
  # If 'npm test' fails, the entire deployment will stop.
  command = "npm run test"

  # This tells Netlify where to find your serverless function entry-point files.
  # We are using the most common and recommended convention.
  functions = "netlify/functions"

# This [[headers]] block applies custom HTTP headers to your function's responses.
# It's the best way to handle CORS on Netlify.
[[headers]]
  # This rule applies to all requests to your 'proxy' function.
  for = "/.netlify/functions/proxy"

  [headers.values]
    # IMPORTANT: Change this to your actual frontend's URL for production security.
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "POST, GET, OPTIONS"
    Access-Control-Allow-Headers = "Content-Type, Authorization"