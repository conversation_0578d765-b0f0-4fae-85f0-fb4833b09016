import { HttpError } from './error.js';

/**
 * Authenticates the incoming request by validating the Bear<PERSON> token.
 * Throws an HttpError if authentication fails.
 * @param {Request} request The incoming request.
 * @param {object} env The environment containing secrets.
 */
export function authenticate(request, env) {
  const authHeader = request.headers.get('Authorization');

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new HttpError('Authorization header is missing or malformed', 401);
  }

  const providedKey = authHeader.split(' ')[1];
  // Get the list of valid keys from the environment variables.
  const validKeys = (env.PROXY_API_KEYS || process.env.PROXY_API_KEYS || '').split(',');

  if (!validKeys.includes(providedKey)) {
    throw new HttpError('Invalid API key', 403);
  }
}

/**
 * Creates the headers for the upstream Google Gemini API call.
 * @param {object} env The environment.
 * @returns {Headers} A Headers object.
 */
export function createGeminiHeaders(env) {
  const headers = new Headers();
  headers.set('Content-Type', 'application/json');
  headers.set('x-goog-api-key', env.GEMINI_API_KEY);
  headers.set('x-goog-api-client', 'gemini-openai-proxy-vercel/1.0.0'); // Identify our proxy
  return headers;
}
