# Project Structure

This document outlines the complete structure of the multi-platform Gemini/OpenAI API Proxy.

## Directory Overview

```
geminitest/
├── api/                          # Vercel Edge Functions entry point
│   └── proxy.js                  # Main Vercel handler
├── cloudflare/                   # Cloudflare Workers implementation
│   ├── handlers/                 # Request handlers
│   │   ├── completions.mjs       # Chat completions handler
│   │   ├── embeddings.mjs        # Embeddings handler
│   │   └── models.mjs            # Models list handler
│   ├── transformers/             # Data transformation utilities
│   │   ├── completions.mjs       # OpenAI ↔ Gemini completions
│   │   ├── embeddings.mjs        # OpenAI ↔ Gemini embeddings
│   │   └── models.mjs            # OpenAI ↔ Gemini models
│   ├── utils/                    # Utility functions
│   │   ├── auth.mjs              # Authentication & headers
│   │   └── error.mjs             # Error handling
│   └── index.mjs                 # Main Cloudflare Worker entry
├── vercel/                       # Vercel Edge Functions implementation
│   ├── handlers/                 # Request handlers (adapted from Cloudflare)
│   │   ├── completions.js        # Chat completions handler
│   │   ├── embeddings.js         # Embeddings handler
│   │   └── models.js             # Models list handler
│   ├── transformers/             # Data transformation utilities
│   │   ├── completions.js        # OpenAI ↔ Gemini completions
│   │   ├── embeddings.js         # OpenAI ↔ Gemini embeddings
│   │   └── models.js             # OpenAI ↔ Gemini models
│   └── utils/                    # Utility functions
│       ├── auth.js               # Authentication & headers
│       ├── cache.js              # In-memory caching
│       └── error.js              # Error handling
├── netlify/                      # Netlify Functions implementation
│   └── functions/
│       └── proxy.js              # Main Netlify function entry
├── src_netlify/                  # Netlify shared modules
│   ├── handlers/                 # Request handlers
│   ├── transformers/             # Data transformation utilities
│   └── utils/                    # Utility functions
├── tests/                        # Test suites
│   ├── cloudflare/               # Cloudflare-specific tests
│   │   ├── handlers/
│   │   ├── transformers/
│   │   ├── utils/
│   │   └── index.test.mjs
│   ├── vercel/                   # Vercel-specific tests
│   │   ├── handlers/
│   │   │   └── models.test.js
│   │   └── proxy.test.js
│   └── netlify/                  # Netlify-specific tests
│       └── handlers/
├── coverage/                     # Test coverage reports
├── node_modules/                 # Dependencies
├── .github/                      # GitHub workflows (empty)
│   └── workflows/
├── package.json                  # Project configuration
├── package-lock.json             # Dependency lock file
├── wrangler.toml                 # Cloudflare Worker configuration
├── vercel.json                   # Vercel deployment configuration
├── netlify.toml                  # Netlify deployment configuration
├── vitest.config.js              # Test configuration
├── README.md                     # Main documentation
├── DEPLOYMENT.md                 # Deployment guide
├── PROJECT_STRUCTURE.md          # This file
└── LICENSE                       # License file
```

## Code Sharing Strategy

### Shared Logic
The core transformation logic is shared between platforms with minimal adaptations:

1. **Request/Response Transformers**: Convert between OpenAI and Gemini API formats
2. **Authentication Logic**: Validate API keys and create headers
3. **Error Handling**: Standardized error responses
4. **Streaming Support**: Server-Sent Events transformation

### Platform-Specific Adaptations

#### Cloudflare Workers
- **File Extension**: `.mjs` (ES modules)
- **Caching**: KV storage for persistent caching
- **Context**: Uses `ctx.waitUntil()` for background operations
- **Runtime**: V8 isolates with Web APIs

#### Vercel Edge Functions
- **File Extension**: `.js` (ES modules)
- **Caching**: In-memory cache (resets on cold starts)
- **Environment**: Process.env for environment variables
- **Runtime**: Edge Runtime (subset of Node.js APIs)

#### Netlify Functions
- **File Extension**: `.js` (CommonJS/ES modules)
- **Caching**: node-cache for in-memory caching
- **Environment**: Process.env for environment variables
- **Runtime**: Full Node.js environment

## API Endpoints

All platforms expose the same OpenAI-compatible endpoints:

### Base URLs
- **Cloudflare**: `https://your-worker.your-subdomain.workers.dev`
- **Vercel**: `https://your-deployment.vercel.app/api/proxy`
- **Netlify**: `https://your-site.netlify.app/.netlify/functions/proxy`

### Endpoints
- `GET /models` - List available Gemini models
- `POST /chat/completions` - Chat completions (streaming/non-streaming)
- `POST /embeddings` - Text embeddings

## Testing Strategy

### Test Organization
- **Unit Tests**: Individual function testing
- **Integration Tests**: Handler-level testing with mocked dependencies
- **Platform Tests**: Platform-specific behavior testing

### Coverage Goals
- **Cloudflare**: 95%+ (production-ready)
- **Vercel**: 80%+ (newly added)
- **Netlify**: 60%+ (existing, needs improvement)

## Development Workflow

### Local Development
```bash
# Cloudflare Workers
npm run start:cf

# Vercel Edge Functions
npm run start:vercel

# Netlify Functions
netlify dev
```

### Testing
```bash
# Run all tests
npm test

# Run with coverage
npm run test -- --coverage
```

### Deployment
```bash
# Deploy to Cloudflare
npm run deploy:cf

# Deploy to Vercel
npm run deploy:vercel

# Deploy to Netlify (auto-deploy on Git push)
git push origin main
```

## Configuration Files

### Platform Configurations
- **`wrangler.toml`**: Cloudflare Worker settings, KV bindings, environment variables
- **`vercel.json`**: Vercel Edge Function settings, CORS headers, environment variables
- **`netlify.toml`**: Netlify Function settings, build commands, CORS headers

### Development Configurations
- **`package.json`**: Dependencies, scripts, Node.js version requirements
- **`vitest.config.js`**: Test configuration and coverage settings
- **`.gitignore`**: Version control exclusions

## Key Features

### 🔄 Request Transformation
- OpenAI → Gemini API format conversion
- Message role mapping (assistant → model)
- Tool/function call handling
- System prompt extraction

### 🌊 Streaming Support
- Real-time Server-Sent Events
- Chunk-by-chunk transformation
- Proper stream termination
- Usage metadata extraction

### 🔐 Security
- API key authentication
- CORS protection
- Safe error handling
- Input validation

### ⚡ Performance
- Response caching (models endpoint)
- Platform-optimized implementations
- Minimal cold start overhead
- Edge deployment support

This structure provides a robust, scalable, and maintainable multi-platform API proxy solution.
