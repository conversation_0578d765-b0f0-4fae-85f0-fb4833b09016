/**
 * Transforms a Gemini models list response into the OpenAI-compatible format.
 * @param {object} geminiData The raw data from the Gemini API.
 * @returns {object} The OpenAI-compatible models list.
 */
export function transformToOpenAIModels(geminiData) {
  return {
    object: 'list',
    data: geminiData.models.map(({ name, displayName, description }) => ({
      id: name.replace('models/', ''),
      object: 'model',
      created: 0, // OpenAI API uses a timestamp, Gemini doesn't provide one.
      owned_by: 'google',
      description: description,
      displayName: displayName,
    })),
  };
}
