import { HttpError } from './error.js';

export function authenticate(event) {
  const authHeader = event.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new HttpError('Authorization header is missing or malformed', 401);
  }

  const providedKey = authHeader.split(' ')[1];
  const validKeys = (process.env.PROXY_API_KEYS || '').split(',');

  if (!validKeys.includes(providedKey)) {
    throw new HttpError('Invalid API key', 403);
  }
}

export function createGeminiHeaders() {
  return {
    'Content-Type': 'application/json',
    'x-goog-api-key': process.env.GEMINI_API_KEY,
    'x-goog-api-client': 'gemini-openai-proxy-netlify/1.0.0',
  };
}