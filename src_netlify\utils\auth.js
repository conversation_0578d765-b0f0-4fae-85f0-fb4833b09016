import { HttpError } from './error.js';

export function authenticate(event) {
  // Handle case-insensitive header lookup (Netlify normalizes headers)
  const authHeader = event.headers.authorization || event.headers.Authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new HttpError('Authorization header is missing or malformed', 401);
  }

  const providedKey = authHeader.split(' ')[1];
  const validKeys = (process.env.PROXY_API_KEYS || '').split(',').filter(key => key.trim());

  if (validKeys.length === 0) {
    throw new HttpError('No valid API keys configured', 500);
  }

  if (!validKeys.includes(providedKey)) {
    throw new HttpError('Invalid API key', 403);
  }
}

export function createGeminiHeaders() {
  const apiKey = process.env.GEMINI_API_KEY;

  if (!apiKey) {
    throw new HttpError('GEMINI_API_KEY environment variable is not configured', 500);
  }

  return {
    'Content-Type': 'application/json',
    'x-goog-api-key': apiKey,
    'x-goog-api-client': 'gemini-openai-proxy-netlify/1.0.0',
  };
}