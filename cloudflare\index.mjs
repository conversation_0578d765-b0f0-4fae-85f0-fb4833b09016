// cloudflare/index.mjs

import { authenticate } from './utils/auth.mjs';
import { createErrorResponse, HttpError } from './utils/error.mjs';
import { handleModels } from './handlers/models.mjs';
import { handleCompletions } from './handlers/completions.mjs'; // <-- IMPORT a
import { handleEmbeddings } from './handlers/embeddings.mjs';

export default {
  async fetch(request, env, ctx) {
    try {
      authenticate(request, env);
      const { pathname } = new URL(request.url);

      switch (true) {
        case pathname.endsWith('/models'):
          return handleModels(request, env, ctx);

        // --- ADD THIS ROUTE ---
        case pathname.endsWith('/chat/completions'):
          return handleCompletions(request, env);

        case pathname.endsWith('/embeddings'):
          return handleEmbeddings(request, env, ctx);

        default:
          throw new HttpError('Not Found', 404);
      }
    } catch (err) {
      return createErrorResponse(err);
    }
  },
};



