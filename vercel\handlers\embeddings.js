// vercel/handlers/embeddings.js

import { HttpError } from '../utils/error.js';
import { createGeminiHeaders } from '../utils/auth.js';
import { 
  transformToGeminiEmbeddingsRequest,
  transformToOpenAIEmbeddingsResponse
} from '../transformers/embeddings.js';

/**
 * Handles embedding requests.
 */
export async function handleEmbeddings(request, env) {
  const openAIRequest = await request.json();
  
  const { geminiRequest, model } = transformToGeminiEmbeddingsRequest(openAIRequest);

  const url = `${env.GEMINI_API_BASE_URL}/${env.GEMINI_API_VERSION}/${model}:batchEmbedContents`;
  const headers = createGeminiHeaders(env);
  
  const upstreamResponse = await fetch(url, {
    method: 'POST',
    headers: headers,
    body: JSON.stringify(geminiRequest),
  });

  if (!upstreamResponse.ok) {
    const errorBody = await upstreamResponse.text();
    console.error('Upstream Embeddings API Error:', errorBody);
    throw new HttpError(`Upstream API error: ${upstreamResponse.status} ${errorBody}`, upstreamResponse.status);
  }

  const geminiResponse = await upstreamResponse.json();
  const openAIResponse = transformToOpenAIEmbeddingsResponse(geminiResponse, openAIRequest.model);

  return new Response(JSON.stringify(openAIResponse), {
    headers: { 'Content-Type': 'application/json' },
  });
}
