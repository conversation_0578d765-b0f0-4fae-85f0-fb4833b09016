{"functions": {"api/proxy.js": {"runtime": "@vercel/edge"}}, "env": {"GEMINI_API_KEY": "@gemini_api_key", "CORS_ALLOWED_ORIGINS": "https://geminit.pizeonicktech1818.dpdns.org", "GEMINI_API_BASE_URL": "https://generativelanguage.googleapis.com", "GEMINI_API_VERSION": "v1beta"}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "https://geminit.pizeonicktech1818.dpdns.org"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}]}