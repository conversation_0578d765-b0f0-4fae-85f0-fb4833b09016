// tests/netlify/handlers/completions.test.js

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { handleCompletions } from '../../../src_netlify/handlers/completions.js';

// Mock the dependencies
vi.mock('../../../src_netlify/utils/error.js', () => ({
  HttpError: class HttpError extends Error {
    constructor(message, status) {
      super(message);
      this.status = status;
    }
  }
}));

vi.mock('../../../src_netlify/utils/auth.js', () => ({
  createGeminiHeaders: vi.fn(() => ({
    'Content-Type': 'application/json',
    'x-goog-api-key': 'test-key'
  }))
}));

vi.mock('../../../src_netlify/transformers/completions.js', () => ({
  transformToGeminiRequest: vi.fn((req) => ({
    contents: [{ role: 'user', parts: [{ text: req.messages[0].content }] }]
  }))
}));

// Mock global fetch
global.fetch = vi.fn();

import { createGeminiHeaders } from '../../../src_netlify/utils/auth.js';
import { transformToGeminiRequest } from '../../../src_netlify/transformers/completions.js';

describe('Netlify Completions Handler', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Set up environment variables
    process.env.GEMINI_API_BASE_URL = 'https://generativelanguage.googleapis.com';
    process.env.GEMINI_API_VERSION = 'v1beta';
  });

  it('should reject streaming requests with helpful error', async () => {
    const event = {
      body: JSON.stringify({
        model: 'gemini-pro',
        messages: [{ role: 'user', content: 'Hello' }],
        stream: true
      })
    };

    const response = await handleCompletions(event);
    
    expect(response.statusCode).toBe(400);
    expect(response.headers['Content-Type']).toBe('application/json');
    
    const body = JSON.parse(response.body);
    expect(body.error.message).toContain('Streaming is not supported on Netlify Functions');
    expect(body.error.type).toBe('unsupported_feature');
    expect(body.error.suggestion).toContain('Remove \'stream: true\'');
  });

  it('should handle non-streaming requests successfully', async () => {
    const mockGeminiResponse = {
      candidates: [{
        content: { parts: [{ text: 'Hello! How can I help you?' }] },
        finishReason: 'STOP'
      }],
      usageMetadata: {
        promptTokenCount: 5,
        candidatesTokenCount: 10,
        totalTokenCount: 15
      }
    };

    fetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockGeminiResponse)
    });

    const event = {
      body: JSON.stringify({
        model: 'gemini-pro',
        messages: [{ role: 'user', content: 'Hello' }]
      })
    };

    const response = await handleCompletions(event);

    expect(response.statusCode).toBe(200);
    expect(response.headers['Content-Type']).toBe('application/json');
    expect(response.headers['Access-Control-Allow-Origin']).toBe('*');

    const body = JSON.parse(response.body);
    expect(body.object).toBe('chat.completion');
    expect(body.model).toBe('gemini-pro');
    expect(body.choices[0].message.content).toBe('Hello! How can I help you?');
    expect(body.choices[0].finish_reason).toBe('stop');
    expect(body.usage.total_tokens).toBe(15);
  });

  it('should handle invalid JSON in request body', async () => {
    const event = {
      body: 'invalid json'
    };

    await expect(handleCompletions(event)).rejects.toThrow('Invalid JSON in request body');
  });

  it('should handle upstream API errors', async () => {
    fetch.mockResolvedValue({
      ok: false,
      status: 500,
      text: () => Promise.resolve('Internal Server Error')
    });

    const event = {
      body: JSON.stringify({
        model: 'gemini-pro',
        messages: [{ role: 'user', content: 'Hello' }]
      })
    };

    await expect(handleCompletions(event)).rejects.toThrow('Upstream API error: 500');
  });

  it('should use environment variable defaults', async () => {
    // Clear environment variables
    delete process.env.GEMINI_API_BASE_URL;
    delete process.env.GEMINI_API_VERSION;

    fetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({
        candidates: [{ content: { parts: [{ text: 'response' }] } }]
      })
    });

    const event = {
      body: JSON.stringify({
        model: 'gemini-pro',
        messages: [{ role: 'user', content: 'Hello' }]
      })
    };

    await handleCompletions(event);

    expect(fetch).toHaveBeenCalledWith(
      'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent',
      expect.any(Object)
    );
  });

  it('should validate required fields', async () => {
    const event = {
      body: JSON.stringify({
        // Missing model field
        messages: [{ role: 'user', content: 'Hello' }]
      })
    };

    await expect(handleCompletions(event)).rejects.toThrow('Missing required field: model');
  });

  it('should validate messages field', async () => {
    const event = {
      body: JSON.stringify({
        model: 'gemini-pro',
        // Missing messages field
      })
    };

    await expect(handleCompletions(event)).rejects.toThrow('Missing or invalid required field: messages');
  });

  it('should reject oversized requests', async () => {
    const largeContent = 'x'.repeat(1024 * 1024 + 1); // > 1MB
    const event = {
      body: JSON.stringify({
        model: 'gemini-pro',
        messages: [{ role: 'user', content: largeContent }]
      })
    };

    await expect(handleCompletions(event)).rejects.toThrow('Request body too large');
  });

  it('should handle tool calls in response', async () => {
    const mockGeminiResponse = {
      candidates: [{
        content: {
          parts: [
            { text: 'I need to check the weather.' },
            { functionCall: { name: 'get_weather', args: { location: 'NYC' } } }
          ]
        },
        finishReason: 'TOOL_CALL'
      }],
      usageMetadata: { promptTokenCount: 5, candidatesTokenCount: 10, totalTokenCount: 15 }
    };

    fetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockGeminiResponse)
    });

    const event = {
      body: JSON.stringify({
        model: 'gemini-pro',
        messages: [{ role: 'user', content: 'What\'s the weather in NYC?' }],
        tools: [{ type: 'function', function: { name: 'get_weather' } }]
      })
    };

    const response = await handleCompletions(event);
    const body = JSON.parse(response.body);

    expect(body.choices[0].message.content).toBe('I need to check the weather.');
    expect(body.choices[0].message.tool_calls).toHaveLength(1);
    expect(body.choices[0].message.tool_calls[0].function.name).toBe('get_weather');
    expect(body.choices[0].message.tool_calls[0].id).toMatch(/^call_/);
    expect(body.choices[0].finish_reason).toBe('tool_calls');
  });

  it('should handle multiple candidates when n > 1', async () => {
    const mockGeminiResponse = {
      candidates: [
        { content: { parts: [{ text: 'Response 1' }] }, finishReason: 'STOP' },
        { content: { parts: [{ text: 'Response 2' }] }, finishReason: 'STOP' }
      ],
      usageMetadata: { promptTokenCount: 5, candidatesTokenCount: 10, totalTokenCount: 15 }
    };

    fetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockGeminiResponse)
    });

    const event = {
      body: JSON.stringify({
        model: 'gemini-pro',
        messages: [{ role: 'user', content: 'Hello' }],
        n: 2
      })
    };

    const response = await handleCompletions(event);
    const body = JSON.parse(response.body);

    expect(body.choices).toHaveLength(2);
    expect(body.choices[0].message.content).toBe('Response 1');
    expect(body.choices[1].message.content).toBe('Response 2');
  });

  it('should handle environment variable validation', async () => {
    // Mock createGeminiHeaders to throw error
    const { createGeminiHeaders } = await import('../../../src_netlify/utils/auth.js');
    createGeminiHeaders.mockImplementation(() => {
      throw new Error('GEMINI_API_KEY environment variable is not configured');
    });

    const event = {
      body: JSON.stringify({
        model: 'gemini-pro',
        messages: [{ role: 'user', content: 'Hello' }]
      })
    };

    await expect(handleCompletions(event)).rejects.toThrow('GEMINI_API_KEY environment variable is not configured');
  });
});
