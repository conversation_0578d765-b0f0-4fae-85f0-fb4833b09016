
import { HttpError } from '../utils/error.js';
import { createGeminiHeaders } from '../utils/auth.js';
import { transformToOpenAIModels } from '../transformers/models.js';
import cache from '../utils/cache.js';

const CACHE_KEY = 'netlify_models_list';

export async function handleModels(event) {
  const cachedData = cache.get(CACHE_KEY);
  if (cachedData) {
    console.log('Returning Netlify models list from cache.');
    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(cachedData),
    };
  }

  const baseUrl = process.env.GEMINI_API_BASE_URL || 'https://generativelanguage.googleapis.com';
  const version = process.env.GEMINI_API_VERSION || 'v1beta';
  const url = `${baseUrl}/${version}/models`;
  const headers = createGeminiHeaders();

  const response = await fetch(url, { headers });
  if (!response.ok) {
    throw new HttpError(`Upstream API error: ${response.status}`, response.status);
  }

  const geminiData = await response.json();
  const openAIResponse = transformToOpenAIModels(geminiData);
  
  cache.set(CACHE_KEY, openAIResponse);

  return {
    statusCode: 200,
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(openAIResponse),
  };
}