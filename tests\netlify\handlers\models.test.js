// We need to import from vitest in an ESM project, not require.
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// We import the real handler to test it.
import { handleModels } from '../../../src_netlify/handlers/models.js';

// We mock the cache utility.
import cache from '../../../src_netlify/utils/cache.js';
vi.mock('../../../src_netlify/utils/cache.js');

describe('Netlify handleModels handler', () => {

  beforeEach(() => {
    // Mock process.env for the test environment
    process.env.GEMINI_API_BASE_URL = 'https://api.gemini.mock';
    process.env.GEMINI_API_VERSION = 'v1beta';
    process.env.GEMINI_API_KEY = 'test-key';
    
    // Clear mock history before each test
    vi.clearAllMocks();
  });

  afterEach(() => {
    // Restore all spies after each test
    vi.restoreAllMocks();
  });

  it('should return from cache if data exists', async () => {
    // ARRANGE
    const event = {}; // Mock event
    const cachedData = { object: 'list', data: [{ id: 'cached-model' }] };
    cache.get.mockReturnValue(cachedData);

    // ACT
    const response = await handleModels(event);

    // ASSERT
    expect(cache.get).toHaveBeenCalledWith('netlify_models_list');
    expect(response.statusCode).toBe(200);
    expect(JSON.parse(response.body)).toEqual(cachedData);
  });

  it('should fetch from upstream when cache is empty', async () => {
    // ARRANGE
    const event = {};
    cache.get.mockReturnValue(null); // Simulate cache miss
    
    const mockApiResponse = { models: [{ name: 'models/gemini-pro' }] };

    // Use the robust fetch spy instead of nock
    const fetchSpy = vi.spyOn(global, 'fetch').mockResolvedValue(
      new Response(JSON.stringify(mockApiResponse), { status: 200 })
    );

    // ACT
    const response = await handleModels(event);

    // ASSERT
    expect(fetchSpy).toHaveBeenCalledOnce();
    expect(cache.set).toHaveBeenCalled(); // Verify it tried to cache the new data
    expect(response.statusCode).toBe(200);
    const body = JSON.parse(response.body);
    expect(body.data[0].id).toBe('gemini-pro');
  });
});