// tests/cloudflare/transformers/completions.test.mjs

import { describe, it, expect } from 'vitest';
import {
  transformToGeminiRequest,
  GeminiToOpenAIStream,
} from '../../../cloudflare/transformers/completions.mjs';

// --- Test Suite for transformToGeminiRequest ---

describe('transformToGeminiRequest', () => {
  it('should transform a basic user message', () => {
    const openAIRequest = {
      messages: [{ role: 'user', content: 'Hello, world!' }],
    };
    const result = transformToGeminiRequest(openAIRequest);
    expect(result.contents).toEqual([{ role: 'user', parts: [{ text: 'Hello, world!' }] }]);
  });

  it('should handle a system prompt by moving it to system_instruction', () => {
    const openAIRequest = {
      messages: [
        { role: 'system', content: 'You are a helpful assistant.' },
        { role: 'user', content: 'What is the weather?' },
      ],
    };
    const result = transformToGeminiRequest(openAIRequest);
    expect(result.system_instruction).toEqual({ parts: [{ text: 'You are a helpful assistant.' }] });
    expect(result.contents.some(c => c.role === 'system')).toBe(false);
    expect(result.contents[0].role).toBe('user');
  });

  it('should map generation config parameters correctly', () => {
    const openAIRequest = {
      messages: [{ role: 'user', content: 'test' }],
      temperature: 0.5,
      top_p: 0.8,
      max_tokens: 150,
      n: 1,
      stop: ['\n', 'stop'],
    };
    const result = transformToGeminiRequest(openAIRequest);
    const config = result.generationConfig;
    expect(config.temperature).toBe(0.5);
    expect(config.topP).toBe(0.8);
    expect(config.maxOutputTokens).toBe(150);
    expect(config.candidateCount).toBe(1);
    expect(config.stopSequences).toEqual(['\n', 'stop']);
  });

  it('should correctly transform tools and adjust their schema', () => {
    const openAIRequest = {
      messages: [{ role: 'user', content: 'test' }],
      tools: [{
        type: 'function',
        function: {
          name: 'get_weather',
          parameters: {
            type: 'object',
            properties: { city: { type: 'string' } },
            additionalProperties: false,
          },
        },
      }],
    };
    const result = transformToGeminiRequest(openAIRequest);
    const toolSchema = result.tools[0].functionDeclarations[0].parameters;
    expect(toolSchema).toBeDefined();
    expect(toolSchema.additionalProperties).toBeUndefined();
    expect(toolSchema.properties.city.type).toBe('string');
  });

  it('should handle tool calls from the assistant', () => {
    const openAIRequest = {
      messages: [{
        role: 'assistant',
        content: null,
        tool_calls: [{
          id: 'call_123',
          type: 'function',
          function: { name: 'get_weather', arguments: '{"city":"Boston"}' },
        }],
      }],
    };
    const result = transformToGeminiRequest(openAIRequest);
    const geminiParts = result.contents[0].parts;
    expect(geminiParts[0].functionCall).toEqual({
      name: 'get_weather',
      args: { city: 'Boston' },
    });
  });

});

// --- Helper Functions for Stream Testing ---

/**
 * Creates a ReadableStream from an array of Gemini-style data chunks.
 */
function createMockGeminiStream(chunks) {
  return new ReadableStream({
    start(controller) {
      for (const chunk of chunks) {
        const line = `data: ${JSON.stringify(chunk)}\n\n`;
        controller.enqueue(new TextEncoder().encode(line));
      }
      controller.close();
    },
  });
}

/**
 * Reads an entire stream and collects the transformed OpenAI chunks.
 * This helper expects a binary stream (Uint8Array).
 */
async function collectStreamedChunks(stream) {
  const reader = stream.getReader();
  const chunks = [];
  let buffer = '';
  const decoder = new TextDecoder();

  while (true) {
    const { done, value } = await reader.read();
    if (done) break;

    buffer += decoder.decode(value, { stream: true });

    let position;
    while ((position = buffer.indexOf('\n\n')) !== -1) {
      const line = buffer.slice(0, position).trim();
      buffer = buffer.slice(position + 2);
      if (line.startsWith('data: ')) {
        const content = line.substring(6);
        if (content === '[DONE]') {
          chunks.push({ done: true });
        } else {
          chunks.push(JSON.parse(content));
        }
      }
    }
  }
  return chunks;
}


// --- Test Suite for GeminiToOpenAIStream ---

describe('GeminiToOpenAIStream', () => {

  it('should transform a simple text chunk', async () => {
    const geminiChunks = [{
      candidates: [{ content: { parts: [{ text: 'Hello' }] } }],
    }];

    const sourceStream = createMockGeminiStream(geminiChunks);
    const transformStream = new GeminiToOpenAIStream('gemini-pro');

    // FIX: Add TextEncoderStream to accurately simulate the final pipeline.
    const resultStream = sourceStream
      .pipeThrough(transformStream)
      .pipeThrough(new TextEncoderStream());

    const openAIChunks = await collectStreamedChunks(resultStream);

    expect(openAIChunks.length).toBe(3); // text chunk, final flush chunk, [DONE]
    const dataChunk = openAIChunks[0];
    expect(dataChunk.choices[0].delta.content).toBe('Hello');
    expect(openAIChunks[2].done).toBe(true);
  });

  it('should handle multiple text chunks correctly', async () => {
    const geminiChunks = [
      { candidates: [{ content: { parts: [{ text: 'Hello' }] } }] },
      { candidates: [{ content: { parts: [{ text: ' world' }] } }] },
    ];

    const sourceStream = createMockGeminiStream(geminiChunks);
    const transformStream = new GeminiToOpenAIStream('gemini-pro');
    
    // FIX: Add TextEncoderStream.
    const resultStream = sourceStream
      .pipeThrough(transformStream)
      .pipeThrough(new TextEncoderStream());
      
    const openAIChunks = await collectStreamedChunks(resultStream);

    expect(openAIChunks[0].choices[0].delta.content).toBe('Hello');
    expect(openAIChunks[1].choices[0].delta.content).toBe(' world');
  });

  it('should transform a tool_call chunk', async () => {
    const geminiChunks = [{
      candidates: [{
        content: { parts: [{ functionCall: { name: 'get_weather', args: { city: 'Tokyo' } } }] },
      }],
    }];
    
    const sourceStream = createMockGeminiStream(geminiChunks);
    const transformStream = new GeminiToOpenAIStream('gemini-pro');

    // FIX: Add TextEncoderStream.
    const resultStream = sourceStream
      .pipeThrough(transformStream)
      .pipeThrough(new TextEncoderStream());
      
    const openAIChunks = await collectStreamedChunks(resultStream);

    const toolCall = openAIChunks[0].choices[0].delta.tool_calls[0];
    expect(toolCall.type).toBe('function');
    expect(toolCall.function.name).toBe('get_weather');
    expect(toolCall.function.args).toEqual({ city: 'Tokyo' });
  });

  it('should include usage and finish_reason in the final chunk', async () => {
    const geminiChunks = [
      { // Final chunk from Gemini
        candidates: [{
          content: { parts: [{ text: '.' }] },
          finishReason: 'STOP',
        }],
        usageMetadata: {
          promptTokenCount: 10,
          candidatesTokenCount: 20,
          totalTokenCount: 30,
        },
      },
    ];

    const sourceStream = createMockGeminiStream(geminiChunks);
    const transformStream = new GeminiToOpenAIStream('gemini-pro');
    
    // FIX: Add TextEncoderStream.
    const resultStream = sourceStream
      .pipeThrough(transformStream)
      .pipeThrough(new TextEncoderStream());
      
    const openAIChunks = await collectStreamedChunks(resultStream);

    // Expect 3 chunks: one with the text '.', one final chunk from flush(), and [DONE].
    expect(openAIChunks.length).toBe(3);

    const textChunk = openAIChunks[0];
    expect(textChunk.choices[0].delta.content).toBe('.');
    
    const finalDataChunk = openAIChunks[1]; // The one generated by flush()
    expect(finalDataChunk.choices[0].finish_reason).toBe('stop');
    expect(finalDataChunk.usage.prompt_tokens).toBe(10);
    expect(finalDataChunk.usage.total_tokens).toBe(30);

    const doneChunk = openAIChunks[2];
    expect(doneChunk.done).toBe(true);
  });
});