// tests/cloudflare/handlers/completions.test.mjs

import { describe, it, expect, vi, beforeEach, afterEach, fail } from 'vitest';
import { handleCompletions } from '../../../cloudflare/handlers/completions.mjs';
import { HttpError } from '../../../cloudflare/utils/error.mjs';

// The definitive mock setup for this module.
vi.mock('../../../cloudflare/transformers/completions.mjs', async (importActual) => {
  const originalModule = await importActual();
  return {
    transformToGeminiRequest: vi.fn((req) => ({ transformedRequest: true, original: req })),
    GeminiToOpenAIStream: originalModule.GeminiToOpenAIStream,
  };
});

// Import the mocked functions AFTER the vi.mock call.
import { transformToGeminiRequest } from '../../../cloudflare/transformers/completions.mjs';

describe('handleCompletions handler', () => {
  let mockEnv;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  beforeEach(() => {
    mockEnv = {
      GEMINI_API_BASE_URL: 'https://api.gemini.mock',
      GEMINI_API_VERSION: 'v1beta',
      GEMINI_API_KEY: 'test-api-key',
    };
  });

  it('should handle a successful streaming request correctly', async () => {
    const openAIRequest = { model: 'gemini-1.5-pro', messages: [{ role: 'user', content: 'Hello' }], stream: true };
    const mockUpstreamStream = new ReadableStream({ start(controller) { controller.enqueue(new TextEncoder().encode('data: {}')); controller.close(); } });
    const fetchSpy = vi.spyOn(global, 'fetch').mockResolvedValue(new Response(mockUpstreamStream, { status: 200 }));
    const request = new Request('https://test.com/v1/chat/completions', { method: 'POST', body: JSON.stringify(openAIRequest) });

    const response = await handleCompletions(request, mockEnv);

    expect(transformToGeminiRequest).toHaveBeenCalledWith(openAIRequest);
    const expectedUrl = `${mockEnv.GEMINI_API_BASE_URL}/${mockEnv.GEMINI_API_VERSION}/models/${openAIRequest.model}:streamGenerateContent?alt=sse`;
    expect(fetchSpy).toHaveBeenCalledWith(expectedUrl, expect.anything());
    expect(response.headers.get('Content-Type')).toBe('text/event-stream');
    expect(response.body).toBeInstanceOf(ReadableStream);
  });

  it('should handle a non-streaming request correctly', async () => {
    const openAIRequest = { model: 'gemini-1.5-pro', messages: [{ role: 'user', content: 'Hello' }], stream: false };
    const mockApiResponse = { result: 'success' };
    const fetchSpy = vi.spyOn(global, 'fetch').mockResolvedValue(new Response(JSON.stringify(mockApiResponse), { status: 200 }));
    const request = new Request('https://test.com/v1/chat/completions', { method: 'POST', body: JSON.stringify(openAIRequest) });
    
    const response = await handleCompletions(request, mockEnv);
    const responseBody = await response.json();
    
    const expectedUrl = `${mockEnv.GEMINI_API_BASE_URL}/${mockEnv.GEMINI_API_VERSION}/models/${openAIRequest.model}:generateContent`;
    expect(fetchSpy).toHaveBeenCalledWith(expectedUrl, expect.anything());
    expect(responseBody).toEqual(mockApiResponse);
  });

  it('should throw an HttpError if the upstream API call fails', async () => {
    const openAIRequest = { model: 'gemini-1.5-pro', messages: [{ role: 'user', content: 'Hello' }] };
    vi.spyOn(global, 'fetch').mockResolvedValue(new Response('Internal Server Error', { status: 500 }));
    const request = new Request('https://test.com/v1/chat/completions', { method: 'POST', body: JSON.stringify(openAIRequest) });
    
    try {
      await handleCompletions(request, mockEnv);
      fail('Expected handleCompletions to throw an error, but it did not.');
    } catch (error) {
      expect(error).toBeInstanceOf(HttpError);
      expect(error.message).toBe('Upstream API error: 500 Internal Server Error');
      expect(error.status).toBe(500);
    }
  });

  it('should throw a 400 HttpError for invalid JSON in the request body', async () => {
    const request = new Request('https://test.com/v1/chat/completions', { method: 'POST', body: '{ not valid json }' });

    try {
      await handleCompletions(request, mockEnv);
      fail('Expected handleCompletions to throw an error, but it did not.');
    } catch (error) {
      expect(error).toBeInstanceOf(HttpError);
      expect(error.message).toBe('Invalid JSON in request body');
      expect(error.status).toBe(400);
    }
  });
});