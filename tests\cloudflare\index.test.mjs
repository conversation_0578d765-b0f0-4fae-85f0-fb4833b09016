// tests/cloudflare/index.test.mjs

import { describe, it, expect, vi, beforeEach } from 'vitest';
import worker from '../../cloudflare/index.mjs';
import { HttpError } from '../../cloudflare/utils/error.mjs';

// --- Mocking Setup ---
vi.mock('../../cloudflare/utils/auth.mjs', () => ({ authenticate: vi.fn() }));
vi.mock('../../cloudflare/utils/error.mjs', async (importOriginal) => {
  const original = await importOriginal();
  return { ...original, createErrorResponse: vi.fn((err) => new Response(JSON.stringify({ error: err.message }), { status: err.status })) };
});
vi.mock('../../cloudflare/handlers/models.mjs', () => ({ handleModels: vi.fn(() => new Response('Handled by models')) }));
vi.mock('../../cloudflare/handlers/completions.mjs', () => ({ handleCompletions: vi.fn(() => new Response('Handled by completions')) }));
vi.mock('../../cloudflare/handlers/embeddings.mjs', () => ({ handleEmbeddings: vi.fn(() => new Response('Handled by embeddings')) }));

// Import the mocked functions AFTER the vi.mock calls.
import { authenticate } from '../../cloudflare/utils/auth.mjs';
import { createErrorResponse } from '../../cloudflare/utils/error.mjs';
import { handleModels } from '../../cloudflare/handlers/models.mjs';
import { handleCompletions } from '../../cloudflare/handlers/completions.mjs';
import { handleEmbeddings } from '../../cloudflare/handlers/embeddings.mjs';

describe('Cloudflare Worker Router (index.mjs)', () => {
  const mockEnv = {};
  const mockCtx = {};

  beforeEach(() => { vi.clearAllMocks(); });

  it('should call the authenticate utility on every request', async () => {
    const request = new Request('https://test.com/models');
    await worker.fetch(request, mockEnv, mockCtx);
    expect(authenticate).toHaveBeenCalledOnce();
  });

  it('should route requests ending in /models to handleModels', async () => {
    const request = new Request('https://test.com/v1/models');
    await worker.fetch(request, mockEnv, mockCtx);
    expect(handleModels).toHaveBeenCalledOnce();
    expect(handleCompletions).not.toHaveBeenCalled();
    expect(handleEmbeddings).not.toHaveBeenCalled();
  });

  it('should route requests ending in /chat/completions to handleCompletions', async () => {
    const request = new Request('https://test.com/v1/chat/completions');
    await worker.fetch(request, mockEnv, mockCtx);
    expect(handleCompletions).toHaveBeenCalledOnce();
    expect(handleModels).not.toHaveBeenCalled();
    expect(handleEmbeddings).not.toHaveBeenCalled();
  });

  it('should route requests ending in /embeddings to handleEmbeddings', async () => {
    const request = new Request('https://test.com/v1/embeddings');
    await worker.fetch(request, mockEnv, mockCtx);
    expect(handleEmbeddings).toHaveBeenCalledOnce();
    expect(handleCompletions).not.toHaveBeenCalled();
    expect(handleModels).not.toHaveBeenCalled();
  });

  it('should return a 404 HttpError for an unknown path', async () => {
    const request = new Request('https://test.com/v1/unknown-path');
    await worker.fetch(request, mockEnv, mockCtx);
    expect(createErrorResponse).toHaveBeenCalledOnce();
    const errorArg = createErrorResponse.mock.calls[0][0];
    expect(errorArg).toBeInstanceOf(HttpError);
    expect(errorArg.status).toBe(404);
  });

  it('should call createErrorResponse if authentication fails', async () => {
    const request = new Request('https://test.com/models');
    const authError = new HttpError('Invalid credentials', 401);
    authenticate.mockImplementation(() => { throw authError; });
    await worker.fetch(request, mockEnv, mockCtx);
    expect(createErrorResponse).toHaveBeenCalledWith(authError);
  });
});