// tests/cloudflare/utils/auth.test.mjs

import { describe, it, expect } from 'vitest';
import { authenticate } from '../../../cloudflare/utils/auth.mjs';
import { HttpError } from '../../../cloudflare/utils/error.mjs';

// --- Test Suite for the authenticate utility ---

describe('authenticate utility', () => {

  // Create a mock environment object that we can use across tests.
  // The list of valid keys is configured here.
  const mockEnv = {
    PROXY_API_KEYS: 'key-1,key-2,secure-key-3',
  };

  // --- Test Case 1: Missing Authorization Header ---

  it('should throw a 401 HttpError if the Authorization header is missing', () => {
    // ARRANGE: Create a request with no Authorization header.
    const request = new Request('https://test.com/path');
    
    // ACT & ASSERT: We expect this to throw an error.
    // We wrap the function call in an arrow function `() => ...` so that `toThrow`
    // can catch the error when it executes the function.
    const action = () => authenticate(request, mockEnv);
    
    expect(action).toThrow(HttpError);
    expect(action).toThrow('Authorization header is missing or malformed');

    // We can also test the properties of the thrown error.
    try {
      action();
    } catch (err) {
      expect(err.status).toBe(401);
    }
  });

  // --- Test Case 2: Malformed Authorization Header ---

  it('should throw a 401 HttpError if the Authorization header is not a Bearer token', () => {
    // ARRANGE: Create a request with a bad header format.
    const request = new Request('https://test.com/path', {
      headers: { 'Authorization': 'Basic dXNlcjpwYXNz' } // Using "Basic" instead of "Bearer"
    });

    const action = () => authenticate(request, mockEnv);

    expect(action).toThrow(HttpError);
    expect(action).toThrow('Authorization header is missing or malformed');
  });

  // --- Test Case 3: Invalid API Key ---

  it('should throw a 403 HttpError if the API key is not in the valid keys list', () => {
    // ARRANGE: Create a request with a key that is not in our mockEnv list.
    const request = new Request('https://test.com/path', {
      headers: { 'Authorization': 'Bearer invalid-key-999' }
    });

    const action = () => authenticate(request, mockEnv);
    
    expect(action).toThrow(HttpError);
    expect(action).toThrow('Invalid API key');
    
    try {
      action();
    } catch (err) {
      expect(err.status).toBe(403);
    }
  });

  // --- Test Case 4: Valid API Key ---

  it('should not throw an error if the API key is valid', () => {
    // ARRANGE: Create a request with one of the valid keys from mockEnv.
    const request = new Request('https://test.com/path', {
      headers: { 'Authorization': 'Bearer key-2' }
    });

    const action = () => authenticate(request, mockEnv);

    // ASSERT: We expect that this function call completes without throwing any error.
    expect(action).not.toThrow();
  });
  
  // --- Test Case 5: Another Valid API Key ---

  it('should not throw an error for another valid API key', () => {
    // ARRANGE
    const request = new Request('https://test.com/path', {
      headers: { 'Authorization': 'Bearer secure-key-3' }
    });

    const action = () => authenticate(request, mockEnv);

    // ASSERT
    expect(action).not.toThrow();
  });
});