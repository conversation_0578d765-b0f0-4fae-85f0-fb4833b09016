{"name": "gemini-proxy-liz54321", "version": "1.0.0", "type": "module", "description": "A secure, multi-platform proxy for OpenAI to Gemini.", "private": true, "scripts": {"test": "vitest run --coverage", "lint": "eslint . --ext .js,.mjs", "start:cf": "wrangler dev cloudflare/index.mjs --local", "deploy:cf": "wrangler deploy cloudflare/index.mjs"}, "dependencies": {"node-cache": "^5.1.2", "pino": "^9.1.0", "vercel": "^42.3.0", "zod": "^3.25.58"}, "devDependencies": {"@vitest/coverage-v8": "^3.2.3", "eslint": "^9.28.0", "nock": "^14.0.5", "vitest": "^3.2.3", "wrangler": "^4.19.1"}, "engines": {"node": ">=20.0.0"}}