// vercel/transformers/completions.js

/**
 * Maps Gemini's finish reason to OpenAI's finish reason.
 * @param {string | null} reason - The finish reason from Gemini (e.g., 'STOP', 'MAX_TOKENS').
 * @returns {string} The OpenAI-compatible finish reason (e.g., 'stop', 'length').
 */
function mapFinishReason(reason) {
  switch (reason) {
    case 'STOP':
      return 'stop';
    case 'MAX_TOKENS':
      return 'length';
    case 'SAFETY':
    case 'RECITATION':
      return 'content_filter';
    case 'TOOL_CALL':
      return 'tool_calls';
    default:
      // If the reason is null or unknown, default to 'stop'.
      return 'stop';
  }
}

/**
 * Transforms the user-facing tool schema from OpenAI format to Gemini format.
 * Specifically, it handles the case where `additionalProperties` is explicitly
 * set to `false`, which Gemini's API rejects.
 * @param {object} schemaPart - A part of the tool's parameter schema.
 */
function adjustToolSchema(schemaPart) {
  if (typeof schemaPart !== 'object' || schemaPart === null) {
    return;
  }
  if (Array.isArray(schemaPart)) {
    schemaPart.forEach(adjustToolSchema);
  } else {
    // Gemini rejects `{"type": "object", "properties": {...}, "additionalProperties": false}`.
    // This removes the `additionalProperties` key if it's explicitly false.
    if (schemaPart.type === 'object' && schemaPart.properties && schemaPart.additionalProperties === false) {
      delete schemaPart.additionalProperties;
    }
    // Recurse through the schema object.
    Object.values(schemaPart).forEach(adjustToolSchema);
  }
}

/**
 * Maps OpenAI message roles to Gemini message roles.
 * @param {string} role - The role from the OpenAI message ('user', 'assistant', 'system', 'tool').
 * @returns {string} The corresponding Gemini role ('user' or 'model').
 */
function mapRole(role) {
  switch (role) {
    case 'assistant':
      return 'model';
    case 'user':
    default:
      return 'user';
  }
}

/**
 * Transforms an array of OpenAI messages into Gemini's `contents` format.
 * This function handles message roles, content types (text, images), and tool calls.
 * @param {Array<object>} messages - The array of messages from the OpenAI request.
 * @returns {Array<object>} The Gemini-compatible `contents` array.
 */
function transformMessages(messages) {
  const contents = [];
  for (const message of messages) {
    if (message.role === 'system') {
      // Gemini doesn't have a 'system' role. We handle this by adding a 'system'
      // instruction field at the top level of the request payload later.
      continue;
    }
    
    // Handle tool messages. Gemini expects tool call responses to be part of the 'user' turn.
    // This is a simplification; a more robust implementation might merge tool calls.
    if (message.role === 'tool') {
      contents.push({
        role: 'user', // In Gemini, tool responses are provided by the user.
        parts: [{
          functionResponse: {
            name: message.tool_call_id, // Simplified mapping. A real scenario might need a name from the original call.
            response: { content: message.content },
          },
        }],
      });
      continue;
    }

    // Handle user and assistant messages.
    const parts = [];
    if (typeof message.content === 'string') {
      parts.push({ text: message.content });
    }
    
    // Handle tool calls from the assistant.
    if (message.tool_calls) {
      message.tool_calls.forEach(toolCall => {
        parts.push({
          functionCall: {
            name: toolCall.function.name,
            args: JSON.parse(toolCall.function.arguments),
          },
        });
      });
    }

    contents.push({
      role: mapRole(message.role),
      parts: parts,
    });
  }
  return contents;
}

/**
 * Transforms an OpenAI chat completion request body into the format required by the Gemini API.
 * @param {object} openAIRequest - The incoming request body from the client.
 * @returns {object} The transformed request body for the Gemini API.
 */
export function transformToGeminiRequest(openAIRequest) {
  const geminiRequest = {};

  // 1. Transform Messages
  geminiRequest.contents = transformMessages(openAIRequest.messages);

  // 2. Extract and prepend System Prompt
  const systemMessage = openAIRequest.messages.find(m => m.role === 'system');
  if (systemMessage) {
    geminiRequest.system_instruction = {
      parts: [{ text: systemMessage.content }],
    };
  }

  // 3. Transform Generation Configuration
  geminiRequest.generationConfig = {
    temperature: openAIRequest.temperature,
    topP: openAIRequest.top_p,
    topK: openAIRequest.top_k,
    maxOutputTokens: openAIRequest.max_tokens,
    candidateCount: openAIRequest.n,
    stopSequences: openAIRequest.stop,
  };

  // 4. Transform Tools
  if (openAIRequest.tools) {
    geminiRequest.tools = openAIRequest.tools.map(tool => {
      // Adjust the schema to be compatible with Gemini.
      adjustToolSchema(tool.function.parameters);
      return { functionDeclarations: [tool.function] };
    }).flat();
  }
  
  // 5. Transform Tool Choice
  if (typeof openAIRequest.tool_choice === 'string' && openAIRequest.tool_choice !== 'auto') {
      geminiRequest.toolConfig = {
          functionCallingConfig: {
              mode: openAIRequest.tool_choice.toUpperCase(), // e.g., 'none', 'any'
          }
      };
  }

  // 6. Set Safety Settings (block none by default for maximum compatibility)
  geminiRequest.safetySettings = [
    { category: 'HARM_CATEGORY_HATE_SPEECH', threshold: 'BLOCK_NONE' },
    { category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT', threshold: 'BLOCK_NONE' },
    { category: 'HARM_CATEGORY_DANGEROUS_CONTENT', threshold: 'BLOCK_NONE' },
    { category: 'HARM_CATEGORY_HARASSMENT', threshold: 'BLOCK_NONE' },
  ];

  return geminiRequest;
}


// --- STREAMING TRANSFORMATION ---

/**
 * A TransformStream that converts a stream of Gemini API responses
 * into an OpenAI-compatible Server-Sent Events (SSE) stream.
 */
export class GeminiToOpenAIStream extends TransformStream {
  constructor(model) {
    let buffer = '';
    let usage = null;
    let finishReason = null;

    super({
      transform(chunk, controller) {
        // The chunk is a Uint8Array, decode it to a string.
        buffer += new TextDecoder().decode(chunk);

        // Process line by line. Gemini streams data with "data: " prefixes.
        let position;
        while ((position = buffer.indexOf('\n')) !== -1) {
          const line = buffer.slice(0, position).trim();
          buffer = buffer.slice(position + 1);

          if (!line.startsWith('data: ')) {
            continue;
          }

          try {
            const geminiChunk = JSON.parse(line.substring(6)); // Remove "data: "

            // Extract usage metadata if present in the final chunk.
            if (geminiChunk.usageMetadata) {
              usage = {
                  prompt_tokens: geminiChunk.usageMetadata.promptTokenCount,
                  completion_tokens: geminiChunk.usageMetadata.candidatesTokenCount,
                  total_tokens: geminiChunk.usageMetadata.totalTokenCount,
              };
            }

            const candidate = geminiChunk.candidates?.[0];
            if (!candidate) continue;

            // Store the latest finish reason.
            finishReason = candidate.finishReason || finishReason;

            const openAIChunk = {
              id: `chatcmpl-${Date.now()}`,
              object: 'chat.completion.chunk',
              created: Math.floor(Date.now() / 1000),
              model: model,
              choices: [],
            };

            const delta = {};
            const contentPart = candidate.content?.parts?.[0];

            if (contentPart?.text) {
              delta.content = contentPart.text;
            }
            if (contentPart?.functionCall) {
                delta.tool_calls = [{
                    index: 0,
                    id: `call_${Date.now()}`, // Generate a temporary ID
                    type: 'function',
                    function: contentPart.functionCall,
                }];
            }

            // Only create a choice if there's content or a tool call.
            if (Object.keys(delta).length > 0) {
              openAIChunk.choices.push({
                index: 0,
                delta: delta,
                finish_reason: null, // Finish reason is sent in the final chunk.
              });
            }

            // Enqueue the transformed chunk if it has choices.
            if (openAIChunk.choices.length > 0) {
              controller.enqueue(`data: ${JSON.stringify(openAIChunk)}\n\n`);
            }

          } catch (e) {
            console.error('Error parsing Gemini stream chunk:', e);
          }
        }
      },

      flush(controller) {
        // When the stream from Gemini is finished, send the final chunk with the finish reason.
        const finalChunk = {
            id: `chatcmpl-${Date.now()}`,
            object: 'chat.completion.chunk',
            created: Math.floor(Date.now() / 1000),
            model: model, // Using the model name passed in the constructor
            choices: [{
                index: 0,
                delta: {},
                // FIX: Use the mapping function to get the correct lowercase value.
                finish_reason: mapFinishReason(finishReason),
            }],
            usage: usage,
        };

        controller.enqueue(`data: ${JSON.stringify(finalChunk)}\n\n`);

        // Send the final [DONE] message to signify the end of the stream.
        controller.enqueue('data: [DONE]\n\n');
        controller.terminate();
      },
    });
  }
}
