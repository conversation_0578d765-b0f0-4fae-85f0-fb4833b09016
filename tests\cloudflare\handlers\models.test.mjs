// tests/cloudflare/handlers/models.test.mjs

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { handleModels } from '../../../cloudflare/handlers/models.mjs';
import { HttpError } from '../../../cloudflare/utils/error.mjs';

// Mock the transformer module.
vi.mock('../../../cloudflare/transformers/models.mjs', () => ({
  transformToOpenAIModels: vi.fn((data) => ({ transformed: true, data })),
}));

// Main test suite for the handler.
describe('handleModels handler', () => {
  let mockEnv;
  let mockCtx;

  // Reset mocks before each test.
  beforeEach(() => {
    vi.clearAllMocks();

    mockEnv = {
      CACHE: {
        get: vi.fn(),
        put: vi.fn(),
      },
      GEMINI_API_BASE_URL: 'https://api.gemini.mock',
      GEMINI_API_VERSION: 'v1',
      GEMINI_API_KEY: 'test-api-key',
    };

    mockCtx = {
      waitUntil: vi.fn(),
    };
  });

  // Test Case 1: Cache HIT
  it('should return a cached response if one exists in the KV store', async () => {
    const cachedResponse = { object: 'list', data: [{ id: 'gemini-pro-cached' }] };
    mockEnv.CACHE.get.mockResolvedValueOnce(cachedResponse);

    const response = await handleModels(new Request('https://test.com/models'), mockEnv, mockCtx);
    const body = await response.json();

    expect(response.status).toBe(200);
    expect(body).toEqual(cachedResponse);
    expect(mockEnv.CACHE.get).toHaveBeenCalledWith('models_list', 'json');
    expect(mockEnv.CACHE.put).not.toHaveBeenCalled();
  });

  // Test Case 2: Cache MISS and successful fetch
  it('should fetch from the upstream API if no cache exists', async () => {
    mockEnv.CACHE.get.mockResolvedValueOnce(null);
    const mockApiResponse = { models: [{ name: 'models/gemini-1.5-pro' }] };

    const fetchSpy = vi.spyOn(global, 'fetch').mockResolvedValue(
      new Response(JSON.stringify(mockApiResponse), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      })
    );

    const response = await handleModels(new Request('https://test.com/models'), mockEnv, mockCtx);
    const body = await response.json();

    expect(response.status).toBe(200);
    expect(body).toEqual({ transformed: true, data: mockApiResponse });
    expect(fetchSpy).toHaveBeenCalledWith(
      `${mockEnv.GEMINI_API_BASE_URL}/${mockEnv.GEMINI_API_VERSION}/models`,
      expect.anything()
    );
    expect(mockCtx.waitUntil).toHaveBeenCalled();
  });

  // Test Case 3: Cache MISS and failed fetch
  it('should throw an HttpError if the upstream API fails', async () => {
    mockEnv.CACHE.get.mockResolvedValueOnce(null);

    vi.spyOn(global, 'fetch').mockResolvedValue(
      new Response(JSON.stringify({ error: 'Upstream server on fire' }), {
        status: 500,
      })
    );
    
    // We need to wrap the async call in a function for `rejects` to work correctly.
    const action = () => handleModels(new Request('https://test.com/models'), mockEnv, mockCtx);

    await expect(action()).rejects.toThrow(HttpError);
    await expect(action()).rejects.toThrow('Upstream API error: 500');
  });
});