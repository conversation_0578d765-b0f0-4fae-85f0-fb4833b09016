// api/proxy.js - Vercel Edge Function

import { authenticate } from '../vercel/utils/auth.js';
import { createErrorResponse, HttpError } from '../vercel/utils/error.js';
import { handleModels } from '../vercel/handlers/models.js';
import { handleCompletions } from '../vercel/handlers/completions.js';
import { handleEmbeddings } from '../vercel/handlers/embeddings.js';

export const config = {
  runtime: 'edge',
};

export default async function handler(request) {
  try {
    // Create env object from Vercel environment variables
    const env = {
      GEMINI_API_KEY: process.env.GEMINI_API_KEY,
      CORS_ALLOWED_ORIGINS: process.env.CORS_ALLOWED_ORIGINS,
      GEMINI_API_BASE_URL: process.env.GEMINI_API_BASE_URL,
      GEMINI_API_VERSION: process.env.GEMINI_API_VERSION,
    };

    authenticate(request, env);
    const { pathname } = new URL(request.url);

    switch (true) {
      case pathname.endsWith('/models'):
        return handleModels(request, env);

      case pathname.endsWith('/chat/completions'):
        return handleCompletions(request, env);

      case pathname.endsWith('/embeddings'):
        return handleEmbeddings(request, env);

      default:
        throw new HttpError('Not Found', 404);
    }
  } catch (err) {
    return createErrorResponse(err);
  }
}
