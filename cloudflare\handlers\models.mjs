import { createErrorResponse, HttpError } from '../utils/error.mjs';
import { createGeminiHeaders } from '../utils/auth.mjs';
import { transformToOpenAIModels } from '../transformers/models.mjs';

/**
 * <PERSON>les requests for the /v1/models endpoint.
 * Implements caching using a KV namespace to reduce upstream calls.
 */
export async function handleModels(request, env, ctx) {
  const CACHE_KEY = 'models_list';

  // 1. Check the cache first.
  const cachedData = await env.CACHE.get(CACHE_KEY, 'json');
  if (cachedData) {
    console.log('Returning models list from cache.');
    return new Response(JSON.stringify(cachedData), { headers: { 'Content-Type': 'application/json' } });
  }
  console.log('Models list not in cache. Fetching from upstream.');

  // 2. If not cached, fetch from the upstream API.
  const url = `${env.GEMINI_API_BASE_URL}/${env.GEMINI_API_VERSION}/models`;
  const headers = createGeminiHeaders(env);
  const response = await fetch(url, { headers });

  if (!response.ok) {
    throw new HttpError(`Upstream API error: ${response.status}`, response.status);
  }

  const geminiData = await response.json();

  // 3. Transform the response to the OpenAI format.
  const openAIResponse = transformToOpenAIModels(geminiData);

  // 4. Store the result in the cache for 1 hour.
  // Use ctx.waitUntil to avoid blocking the response to the user.
  ctx.waitUntil(env.CACHE.put(CACHE_KEY, JSON.stringify(openAIResponse), { expirationTtl: 3600 }));

  // 5. Return the transformed response.
  return new Response(JSON.stringify(openAIResponse), { headers: { 'Content-Type': 'application/json' } });
}
