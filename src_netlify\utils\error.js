export class HttpError extends Error {
  constructor(message, status) {
    super(message);
    this.name = 'HttpError';
    this.status = status || 500;
  }
}

export function createErrorResponse(err) {
  const statusCode = err.status || 500;
  console.error(`Netlify Function Error (status ${statusCode}):`, err);

  const errorBody = {
    error: {
      message: statusCode >= 500 ? 'An internal server error occurred.' : err.message,
      type: err.name,
    },
  };

  return {
    statusCode,
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(errorBody),
  };
}