export class HttpError extends Error {
  constructor(message, status) {
    super(message);
    this.name = 'HttpError';
    this.status = status || 500;
  }
}

/**
 * Creates consistent CORS headers for all responses
 */
export function createCorsHeaders() {
  const corsOrigin = process.env.CORS_ALLOWED_ORIGINS || '*';

  return {
    'Access-Control-Allow-Origin': corsOrigin,
    'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Credentials': 'false',
  };
}

export function createErrorResponse(err) {
  const statusCode = err.status || 500;
  console.error(`Netlify Function Error (status ${statusCode}):`, err);

  const errorBody = {
    error: {
      message: statusCode >= 500 ? 'An internal server error occurred.' : err.message,
      type: err.name,
      code: statusCode,
    },
  };

  return {
    statusCode,
    headers: {
      'Content-Type': 'application/json',
      ...createCorsHeaders(),
    },
    body: JSON.stringify(errorBody),
  };
}