export class HttpError extends Error {
  constructor(message, status) {
    super(message);
    this.name = 'HttpError';
    this.status = status || 500;
  }
}

export function createErrorResponse(err) {
  const statusCode = err.status || 500;
  console.error(`Netlify Function Error (status ${statusCode}):`, err);

  const errorBody = {
    error: {
      message: statusCode >= 500 ? 'An internal server error occurred.' : err.message,
      type: err.name,
      code: statusCode,
    },
  };

  return {
    statusCode,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
    body: JSON.stringify(errorBody),
  };
}