import { HttpError, createCorsHeaders } from '../utils/error.js';
import { createGeminiHeaders } from '../utils/auth.js';
import { 
  transformToGeminiEmbeddingsRequest,
  transformToOpenAIEmbeddingsResponse
} from '../transformers/embeddings.js';

/**
 * <PERSON>les embedding requests for Netlify Functions.
 */
export async function handleEmbeddings(event) {
  // Check request size
  const maxRequestSize = 1024 * 1024; // 1MB
  if (event.body && event.body.length > maxRequestSize) {
    throw new HttpError('Request body too large. Maximum size is 1MB.', 413);
  }

  let openAIRequest;
  try {
    openAIRequest = JSON.parse(event.body);
  } catch (err) {
    throw new HttpError('Invalid JSON in request body', 400);
  }

  // Validate required fields
  if (!openAIRequest.input) {
    throw new HttpError('Missing required field: input', 400);
  }
  
  const { geminiRequest, model } = transformToGeminiEmbeddingsRequest(openAIRequest);

  const baseUrl = process.env.GEMINI_API_BASE_URL || 'https://generativelanguage.googleapis.com';
  const version = process.env.GEMINI_API_VERSION || 'v1beta';
  const url = `${baseUrl}/${version}/${model}:batchEmbedContents`;
  
  const headers = createGeminiHeaders();
  
  const upstreamResponse = await fetch(url, {
    method: 'POST',
    headers,
    body: JSON.stringify(geminiRequest),
  });

  if (!upstreamResponse.ok) {
    const errorBody = await upstreamResponse.text();
    console.error('Upstream Embeddings API Error:', errorBody);
    throw new HttpError(`Upstream API error: ${upstreamResponse.status} ${errorBody}`, upstreamResponse.status);
  }

  const geminiResponse = await upstreamResponse.json();
  const openAIResponse = transformToOpenAIEmbeddingsResponse(geminiResponse, openAIRequest.model);

  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      ...createCorsHeaders(),
    },
    body: JSON.stringify(openAIResponse),
  };
}
