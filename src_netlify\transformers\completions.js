// Netlify completions transformer - non-streaming only

// --- Request Transformer (Full implementation from the Cloudflare version) ---

// Helper functions (these can be moved to a shared utility if you want)
function adjustToolSchema(schemaPart) {
  if (typeof schemaPart !== 'object' || schemaPart === null) return;
  if (Array.isArray(schemaPart)) {
    schemaPart.forEach(adjustToolSchema);
  } else {
    if (schemaPart.type === 'object' && schemaPart.properties && schemaPart.additionalProperties === false) {
      delete schemaPart.additionalProperties;
    }
    Object.values(schemaPart).forEach(adjustToolSchema);
  }
}

function mapRole(role) {
  return role === 'assistant' ? 'model' : 'user';
}

function transformMessages(messages) {
  const contents = [];
  for (const message of messages) {
    if (message.role === 'system') continue;
    if (message.role === 'tool') {
      contents.push({ role: 'user', parts: [{ functionResponse: { name: message.tool_call_id, response: { content: message.content } } }] });
      continue;
    }
    const parts = [];
    if (typeof message.content === 'string') parts.push({ text: message.content });
    if (message.tool_calls) {
      message.tool_calls.forEach(toolCall => {
        parts.push({ functionCall: { name: toolCall.function.name, args: JSON.parse(toolCall.function.arguments) } });
      });
    }
    contents.push({ role: mapRole(message.role), parts });
  }
  return contents;
}

// Use `export` for the main function.
export function transformToGeminiRequest(openAIRequest) {
  const geminiRequest = {};
  geminiRequest.contents = transformMessages(openAIRequest.messages);
  const systemMessage = openAIRequest.messages.find(m => m.role === 'system');
  if (systemMessage) {
    geminiRequest.system_instruction = { parts: [{ text: systemMessage.content }] };
  }
  geminiRequest.generationConfig = {
    temperature: openAIRequest.temperature,
    topP: openAIRequest.top_p,
    maxOutputTokens: openAIRequest.max_tokens,
  };
  if (openAIRequest.tools) {
    geminiRequest.tools = openAIRequest.tools.map(tool => {
      adjustToolSchema(tool.function.parameters);
      return { functionDeclarations: [tool.function] };
    }).flat();
  }
  return geminiRequest;
}

