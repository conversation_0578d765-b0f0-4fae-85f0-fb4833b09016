// Netlify completions transformer - non-streaming only

// --- Request Transformer (Full implementation from the Cloudflare version) ---

// Helper functions (these can be moved to a shared utility if you want)
function adjustToolSchema(schemaPart) {
  if (typeof schemaPart !== 'object' || schemaPart === null) return;
  if (Array.isArray(schemaPart)) {
    schemaPart.forEach(adjustToolSchema);
  } else {
    if (schemaPart.type === 'object' && schemaPart.properties && schemaPart.additionalProperties === false) {
      delete schemaPart.additionalProperties;
    }
    Object.values(schemaPart).forEach(adjustToolSchema);
  }
}

function mapRole(role) {
  return role === 'assistant' ? 'model' : 'user';
}

function transformMessages(messages) {
  const contents = [];
  for (const message of messages) {
    if (message.role === 'system') continue;
    if (message.role === 'tool') {
      contents.push({ role: 'user', parts: [{ functionResponse: { name: message.tool_call_id, response: { content: message.content } } }] });
      continue;
    }
    const parts = [];
    if (typeof message.content === 'string') parts.push({ text: message.content });
    if (message.tool_calls) {
      message.tool_calls.forEach(toolCall => {
        parts.push({ functionCall: { name: toolCall.function.name, args: JSON.parse(toolCall.function.arguments) } });
      });
    }
    contents.push({ role: mapRole(message.role), parts });
  }
  return contents;
}

// Use `export` for the main function.
export function transformToGeminiRequest(openAIRequest) {
  const geminiRequest = {};
  geminiRequest.contents = transformMessages(openAIRequest.messages);
  const systemMessage = openAIRequest.messages.find(m => m.role === 'system');
  if (systemMessage) {
    geminiRequest.system_instruction = { parts: [{ text: systemMessage.content }] };
  }
  geminiRequest.generationConfig = {
    temperature: openAIRequest.temperature,
    topP: openAIRequest.top_p,
    topK: openAIRequest.top_k,
    maxOutputTokens: openAIRequest.max_tokens,
    candidateCount: openAIRequest.n,
    stopSequences: openAIRequest.stop,
  };
  // 4. Transform Tools
  if (openAIRequest.tools) {
    geminiRequest.tools = openAIRequest.tools.map(tool => {
      // Adjust the schema to be compatible with Gemini.
      adjustToolSchema(tool.function.parameters);
      return { functionDeclarations: [tool.function] };
    }).flat();
  }

  // 5. Transform Tool Choice
  if (typeof openAIRequest.tool_choice === 'string' && openAIRequest.tool_choice !== 'auto') {
      geminiRequest.toolConfig = {
          functionCallingConfig: {
              mode: openAIRequest.tool_choice.toUpperCase(), // e.g., 'none', 'any'
          }
      };
  }

  // 6. Set Safety Settings (block none by default for maximum compatibility)
  geminiRequest.safetySettings = [
    { category: 'HARM_CATEGORY_HATE_SPEECH', threshold: 'BLOCK_NONE' },
    { category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT', threshold: 'BLOCK_NONE' },
    { category: 'HARM_CATEGORY_DANGEROUS_CONTENT', threshold: 'BLOCK_NONE' },
    { category: 'HARM_CATEGORY_HARASSMENT', threshold: 'BLOCK_NONE' },
  ];

  return geminiRequest;
}

