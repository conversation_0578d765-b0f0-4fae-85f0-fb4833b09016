// Use `import` to get the Transform class from Node.js's built-in stream module.
import { Transform } from 'stream';

// --- Request Transformer (Full implementation from the Cloudflare version) ---

// Helper functions (these can be moved to a shared utility if you want)
function adjustToolSchema(schemaPart) {
  if (typeof schemaPart !== 'object' || schemaPart === null) return;
  if (Array.isArray(schemaPart)) {
    schemaPart.forEach(adjustToolSchema);
  } else {
    if (schemaPart.type === 'object' && schemaPart.properties && schemaPart.additionalProperties === false) {
      delete schemaPart.additionalProperties;
    }
    Object.values(schemaPart).forEach(adjustToolSchema);
  }
}

function mapRole(role) {
  return role === 'assistant' ? 'model' : 'user';
}

function transformMessages(messages) {
  const contents = [];
  for (const message of messages) {
    if (message.role === 'system') continue;
    if (message.role === 'tool') {
      contents.push({ role: 'user', parts: [{ functionResponse: { name: message.tool_call_id, response: { content: message.content } } }] });
      continue;
    }
    const parts = [];
    if (typeof message.content === 'string') parts.push({ text: message.content });
    if (message.tool_calls) {
      message.tool_calls.forEach(toolCall => {
        parts.push({ functionCall: { name: toolCall.function.name, args: JSON.parse(toolCall.function.arguments) } });
      });
    }
    contents.push({ role: mapRole(message.role), parts });
  }
  return contents;
}

// Use `export` for the main function.
export function transformToGeminiRequest(openAIRequest) {
  const geminiRequest = {};
  geminiRequest.contents = transformMessages(openAIRequest.messages);
  const systemMessage = openAIRequest.messages.find(m => m.role === 'system');
  if (systemMessage) {
    geminiRequest.system_instruction = { parts: [{ text: systemMessage.content }] };
  }
  geminiRequest.generationConfig = {
    temperature: openAIRequest.temperature,
    topP: openAIRequest.top_p,
    maxOutputTokens: openAIRequest.max_tokens,
  };
  if (openAIRequest.tools) {
    geminiRequest.tools = openAIRequest.tools.map(tool => {
      adjustToolSchema(tool.function.parameters);
      return { functionDeclarations: [tool.function] };
    }).flat();
  }
  return { geminiRequest, model: openAIRequest.model };
}


// --- Node.js Stream Transformer ---

// Use `export` for the class.
export class GeminiToOpenAIStream extends Transform {
  constructor(model) {
    super({ encoding: 'utf8' }); // Good practice to set encoding
    this.model = model;
    this.buffer = '';
  }

  _transform(chunk, encoding, callback) {
    this.buffer += chunk.toString();
    let position;
    while ((position = this.buffer.indexOf('\n')) !== -1) {
      const line = this.buffer.slice(0, position).trim();
      this.buffer = this.buffer.slice(position + 1);
      if (line.startsWith('data: ')) {
        try {
          const geminiChunk = JSON.parse(line.substring(6));
          const textContent = geminiChunk.candidates?.[0]?.content?.parts?.[0]?.text;
          if (textContent) {
            const openAIChunk = {
              id: `chatcmpl-${Date.now()}`,
              object: 'chat.completion.chunk',
              created: Math.floor(Date.now() / 1000),
              model: this.model,
              choices: [{ delta: { content: textContent }, finish_reason: null }],
            };
            this.push(`data: ${JSON.stringify(openAIChunk)}\n\n`);
          }
        } catch (e) {
          // It's better to log the error than to ignore it silently.
          console.error('Failed to parse stream chunk:', e);
        }
      }
    }
    callback();
  }

  _flush(callback) {
    // A full implementation would map the finish_reason here.
    this.push('data: [DONE]\n\n');
    callback();
  }
}