// tests/vercel/proxy.test.js

import { describe, it, expect, vi, beforeEach } from 'vitest';

// Mock the handlers and utilities
vi.mock('../../vercel/utils/auth.js', () => ({ authenticate: vi.fn() }));
vi.mock('../../vercel/utils/error.js', async (importOriginal) => {
  const original = await importOriginal();
  return { 
    ...original, 
    createErrorResponse: vi.fn((err) => new Response(JSON.stringify({ error: err.message }), { status: err.status || 500 })) 
  };
});
vi.mock('../../vercel/handlers/models.js', () => ({ handleModels: vi.fn(() => new Response('Handled by models')) }));
vi.mock('../../vercel/handlers/completions.js', () => ({ handleCompletions: vi.fn(() => new Response('Handled by completions')) }));
vi.mock('../../vercel/handlers/embeddings.js', () => ({ handleEmbeddings: vi.fn(() => new Response('Handled by embeddings')) }));

// Import the handler after mocking
import handler from '../../api/proxy.js';
import { authenticate } from '../../vercel/utils/auth.js';
import { createErrorResponse, HttpError } from '../../vercel/utils/error.js';
import { handleModels } from '../../vercel/handlers/models.js';
import { handleCompletions } from '../../vercel/handlers/completions.js';
import { handleEmbeddings } from '../../vercel/handlers/embeddings.js';

describe('Vercel Proxy Handler', () => {
  // Mock environment variables
  const originalEnv = process.env;

  beforeEach(() => {
    vi.clearAllMocks();
    process.env = {
      ...originalEnv,
      GEMINI_API_KEY: 'test-key',
      CORS_ALLOWED_ORIGINS: 'https://test.com',
      GEMINI_API_BASE_URL: 'https://generativelanguage.googleapis.com',
      GEMINI_API_VERSION: 'v1beta'
    };
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  it('should call authenticate on every request', async () => {
    const request = new Request('https://test.com/api/proxy/models');
    await handler(request);
    expect(authenticate).toHaveBeenCalledOnce();
  });

  it('should route requests ending in /models to handleModels', async () => {
    const request = new Request('https://test.com/api/proxy/v1/models');
    await handler(request);
    expect(handleModels).toHaveBeenCalledOnce();
    expect(handleCompletions).not.toHaveBeenCalled();
    expect(handleEmbeddings).not.toHaveBeenCalled();
  });

  it('should route requests ending in /chat/completions to handleCompletions', async () => {
    const request = new Request('https://test.com/api/proxy/v1/chat/completions');
    await handler(request);
    expect(handleCompletions).toHaveBeenCalledOnce();
    expect(handleModels).not.toHaveBeenCalled();
    expect(handleEmbeddings).not.toHaveBeenCalled();
  });

  it('should route requests ending in /embeddings to handleEmbeddings', async () => {
    const request = new Request('https://test.com/api/proxy/v1/embeddings');
    await handler(request);
    expect(handleEmbeddings).toHaveBeenCalledOnce();
    expect(handleCompletions).not.toHaveBeenCalled();
    expect(handleModels).not.toHaveBeenCalled();
  });

  it('should return a 404 error for unknown paths', async () => {
    const request = new Request('https://test.com/api/proxy/v1/unknown-path');
    await handler(request);
    expect(createErrorResponse).toHaveBeenCalledOnce();
    const errorArg = createErrorResponse.mock.calls[0][0];
    expect(errorArg).toBeInstanceOf(HttpError);
    expect(errorArg.status).toBe(404);
  });

  it('should handle authentication errors', async () => {
    const request = new Request('https://test.com/api/proxy/models');
    const authError = new HttpError('Invalid credentials', 401);
    authenticate.mockImplementation(() => { throw authError; });
    
    await handler(request);
    expect(createErrorResponse).toHaveBeenCalledWith(authError);
  });

  it('should pass environment variables correctly', async () => {
    const request = new Request('https://test.com/api/proxy/models');
    await handler(request);
    
    expect(authenticate).toHaveBeenCalledWith(request, {
      GEMINI_API_KEY: 'test-key',
      CORS_ALLOWED_ORIGINS: 'https://test.com',
      GEMINI_API_BASE_URL: 'https://generativelanguage.googleapis.com',
      GEMINI_API_VERSION: 'v1beta'
    });
  });
});
