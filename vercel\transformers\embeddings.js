// vercel/transformers/embeddings.js

/**
 * Transforms an OpenAI embeddings request into the format for Gemini's batchEmbedContents endpoint.
 * @param {object} openAIRequest The incoming OpenAI request body.
 * @returns {{ geminiRequest: object, model: string }} The transformed request for <PERSON> and the resolved model name.
 */
export function transformToGeminiEmbeddingsRequest(openAIRequest) {
  // Normalize the input to always be an array of strings.
  const inputs = Array.isArray(openAIRequest.input) ? openAIRequest.input : [openAIRequest.input];
  
  // Resolve the model name. Gemini expects the 'models/' prefix.
  const model = `models/${openAIRequest.model || 'text-embedding-004'}`;

  const geminiRequest = {
    requests: inputs.map(text => ({
      model: model,
      content: {
        parts: [{ text }],
      },
    })),
  };

  return { geminiRequest, model };
}

/**
 * Transforms a Gemini batchEmbedContents response into the OpenAI-compatible format.
 * @param {object} geminiResponse The raw response from the Gemini API.
 * @param {string} model The original OpenAI model name used in the request.
 * @returns {object} The OpenAI-compatible embeddings response.
 */
export function transformToOpenAIEmbeddingsResponse(geminiResponse, model) {
  const openAIResponse = {
    object: 'list',
    data: geminiResponse.embeddings.map((embedding, index) => ({
      object: 'embedding',
      index: index,
      embedding: embedding.values,
    })),
    model: model,
    usage: {
      // Gemini's embedding models do not currently return token usage.
      prompt_tokens: 0,
      total_tokens: 0,
    },
  };

  return openAIResponse;
}
