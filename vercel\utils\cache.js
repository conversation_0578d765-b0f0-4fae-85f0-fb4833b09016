/**
 * Simple in-memory cache for Vercel Edge Functions
 * Note: This cache is per-instance and will be reset on cold starts
 * For production, consider using external cache like Redis or Vercel KV
 */

const cache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds

/**
 * Get a value from cache
 * @param {string} key - Cache key
 * @returns {any|null} - Cached value or null if not found/expired
 */
export function getCache(key) {
  const item = cache.get(key);
  if (!item) return null;
  
  if (Date.now() > item.expiry) {
    cache.delete(key);
    return null;
  }
  
  return item.value;
}

/**
 * Set a value in cache
 * @param {string} key - Cache key
 * @param {any} value - Value to cache
 * @param {number} ttl - Time to live in milliseconds (optional, defaults to CACHE_TTL)
 */
export function setCache(key, value, ttl = CACHE_TTL) {
  cache.set(key, {
    value,
    expiry: Date.now() + ttl
  });
}

/**
 * Delete a value from cache
 * @param {string} key - Cache key
 */
export function deleteCache(key) {
  cache.delete(key);
}

/**
 * Clear all cache
 */
export function clearCache() {
  cache.clear();
}
