// tests/netlify/proxy.test.js

import { describe, it, expect, vi, beforeEach } from 'vitest';

// Mock the handlers and utilities
vi.mock('../../src_netlify/utils/auth.js', () => ({ authenticate: vi.fn() }));
vi.mock('../../src_netlify/utils/error.js', async (importOriginal) => {
  const original = await importOriginal();
  return { 
    ...original, 
    createErrorResponse: vi.fn((err) => ({
      statusCode: err.status || 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: err.message })
    }))
  };
});
vi.mock('../../src_netlify/handlers/models.js', () => ({ 
  handleModels: vi.fn(() => Promise.resolve({ statusCode: 200, body: 'Models handled' }))
}));
vi.mock('../../src_netlify/handlers/completions.js', () => ({ 
  handleCompletions: vi.fn(() => Promise.resolve({ statusCode: 200, body: 'Completions handled' }))
}));
vi.mock('../../src_netlify/handlers/embeddings.js', () => ({ 
  handleEmbeddings: vi.fn(() => Promise.resolve({ statusCode: 200, body: 'Embeddings handled' }))
}));

// Import the handler after mocking
import { handler } from '../../netlify/functions/proxy.js';
import { authenticate } from '../../src_netlify/utils/auth.js';
import { createErrorResponse, HttpError } from '../../src_netlify/utils/error.js';
import { handleModels } from '../../src_netlify/handlers/models.js';
import { handleCompletions } from '../../src_netlify/handlers/completions.js';
import { handleEmbeddings } from '../../src_netlify/handlers/embeddings.js';

describe('Netlify Proxy Handler', () => {
  const mockContext = {};

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should handle OPTIONS preflight requests', async () => {
    const event = {
      httpMethod: 'OPTIONS',
      path: '/.netlify/functions/proxy/models'
    };

    const response = await handler(event, mockContext);
    
    expect(response.statusCode).toBe(204);
    expect(response.headers['Access-Control-Allow-Origin']).toBe('*');
    expect(response.headers['Access-Control-Allow-Methods']).toBe('POST, GET, OPTIONS');
    expect(authenticate).not.toHaveBeenCalled();
  });

  it('should call authenticate on non-OPTIONS requests', async () => {
    const event = {
      httpMethod: 'GET',
      path: '/.netlify/functions/proxy/models'
    };

    await handler(event, mockContext);
    expect(authenticate).toHaveBeenCalledWith(event);
  });

  it('should route requests ending in /models to handleModels', async () => {
    const event = {
      httpMethod: 'GET',
      path: '/.netlify/functions/proxy/v1/models'
    };

    const response = await handler(event, mockContext);
    
    expect(handleModels).toHaveBeenCalledWith(event);
    expect(handleCompletions).not.toHaveBeenCalled();
    expect(handleEmbeddings).not.toHaveBeenCalled();
    expect(response.statusCode).toBe(200);
  });

  it('should route requests ending in /chat/completions to handleCompletions', async () => {
    const event = {
      httpMethod: 'POST',
      path: '/.netlify/functions/proxy/v1/chat/completions'
    };

    const response = await handler(event, mockContext);
    
    expect(handleCompletions).toHaveBeenCalledWith(event);
    expect(handleModels).not.toHaveBeenCalled();
    expect(handleEmbeddings).not.toHaveBeenCalled();
    expect(response.statusCode).toBe(200);
  });

  it('should route requests ending in /embeddings to handleEmbeddings', async () => {
    const event = {
      httpMethod: 'POST',
      path: '/.netlify/functions/proxy/v1/embeddings'
    };

    const response = await handler(event, mockContext);
    
    expect(handleEmbeddings).toHaveBeenCalledWith(event);
    expect(handleCompletions).not.toHaveBeenCalled();
    expect(handleModels).not.toHaveBeenCalled();
    expect(response.statusCode).toBe(200);
  });

  it('should return 404 for unknown paths', async () => {
    const event = {
      httpMethod: 'GET',
      path: '/.netlify/functions/proxy/v1/unknown-path'
    };

    const response = await handler(event, mockContext);
    
    expect(response.statusCode).toBe(404);
    expect(response.headers['Content-Type']).toBe('application/json');
    
    const body = JSON.parse(response.body);
    expect(body.error.message).toBe('Not Found');
    expect(body.error.type).toBe('invalid_request_error');
    expect(body.error.code).toBe(404);
  });

  it('should handle authentication errors', async () => {
    const event = {
      httpMethod: 'GET',
      path: '/.netlify/functions/proxy/models'
    };
    
    const authError = new HttpError('Invalid credentials', 401);
    authenticate.mockImplementation(() => { throw authError; });
    
    const response = await handler(event, mockContext);
    expect(createErrorResponse).toHaveBeenCalledWith(authError);
  });

  it('should handle handler errors', async () => {
    const event = {
      httpMethod: 'GET',
      path: '/.netlify/functions/proxy/models'
    };
    
    const handlerError = new Error('Handler failed');
    handleModels.mockRejectedValue(handlerError);
    
    const response = await handler(event, mockContext);
    expect(createErrorResponse).toHaveBeenCalledWith(handlerError);
  });

  it('should strip netlify function path correctly', async () => {
    const event = {
      httpMethod: 'GET',
      path: '/.netlify/functions/proxy/v1/models'
    };

    await handler(event, mockContext);

    // The path should be stripped to '/v1/models' and match the endsWith check
    expect(handleModels).toHaveBeenCalled();
  });

  it('should validate HTTP methods for endpoints', async () => {
    const event = {
      httpMethod: 'POST',
      path: '/.netlify/functions/proxy/models'
    };

    const response = await handler(event, mockContext);
    expect(createErrorResponse).toHaveBeenCalledOnce();
    const errorArg = createErrorResponse.mock.calls[0][0];
    expect(errorArg.message).toContain('Method POST not allowed for /models endpoint');
    expect(errorArg.status).toBe(405);
  });

  it('should allow correct HTTP methods', async () => {
    const event = {
      httpMethod: 'POST',
      path: '/.netlify/functions/proxy/chat/completions'
    };

    await handler(event, mockContext);
    expect(handleCompletions).toHaveBeenCalled();
    expect(createErrorResponse).not.toHaveBeenCalled();
  });
});
