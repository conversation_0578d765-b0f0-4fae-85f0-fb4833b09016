# Secure Multi-Platform Gemini/OpenAI API Proxy

This repository contains a professional, production-ready API proxy to translate OpenAI API requests to the Google Gemini API. It includes two separate, optimized implementations for deployment on **Cloudflare Workers** and **Netlify Functions**.

## Features

- **Platform-Idiomatic:** Separate, clean implementations for Cloudflare and Netlify.
- **Secure by Default:** Strict CORS, API key authentication, and safe error handling.
- **Performant:** Implements caching for the `/models` endpoint on both platforms.
- **Maintainable:** Fully modular code with a clear separation of concerns.
- **Quality Assured:** Includes a full test suite and a CI pipeline to ensure code quality.

## Project Structure

- `/.github/workflows/`: CI pipeline for automated testing.
- `/cloudflare/`: Self-contained Cloudflare Worker application.
- `/netlify/`: Netlify Function entry point.
- `/src_netlify/`: Shared modules for the Netlify application.
- `/tests/`: All unit and integration tests.
- `wrangler.toml`: Configuration for the Cloudflare Worker.
- `netlify.toml`: Configuration for the Netlify deployment.

## Getting Started

### Prerequisites

- Node.js v20+
- A Cloudflare account
- A Netlify account
- A Google Gemini API Key

### Installation

```bash
npm install
