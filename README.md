# Secure Multi-Platform Gemini/OpenAI API Proxy

This repository contains a professional, production-ready API proxy to translate OpenAI API requests to the Google Gemini API. It includes three separate, optimized implementations for deployment on **Cloudflare Workers**, **Vercel Edge Functions**, and **Netlify Functions**.

## Features

- **Platform-Idiomatic:** Separate, clean implementations for Cloudflare, Vercel, and Netlify.
- **Secure by Default:** Strict CORS, API key authentication, and safe error handling.
- **Performant:** Implements caching for the `/models` endpoint on all platforms.
- **Maintainable:** Fully modular code with a clear separation of concerns.
- **Quality Assured:** Includes a full test suite and a CI pipeline to ensure code quality.

## Project Structure

- `/.github/workflows/`: CI pipeline for automated testing.
- `/cloudflare/`: Self-contained Cloudflare Worker application.
- `/vercel/`: Vercel Edge Functions implementation (shared code with Cloudflare).
- `/netlify/`: Netlify Function entry point.
- `/src_netlify/`: Shared modules for the Netlify application.
- `/tests/`: All unit and integration tests.
- `wrangler.toml`: Configuration for the Cloudflare Worker.
- `vercel.json`: Configuration for the Vercel deployment.
- `netlify.toml`: Configuration for the Netlify deployment.

## Getting Started

### Prerequisites

- Node.js v20+
- A Cloudflare account
- A Vercel account
- A Netlify account
- A Google Gemini API Key

### Installation

```bash
npm install
```

## Deployment

### Cloudflare Workers

1. **Configure your environment:**
   ```bash
   # Set your Gemini API key as a secret
   wrangler secret put GEMINI_API_KEY
   wrangler secret put PROXY_API_KEYS  # Comma-separated list of valid API keys
   ```

2. **Update `wrangler.toml`:**
   - Change the `name` to your preferred worker name
   - Update `CORS_ALLOWED_ORIGINS` to your frontend URL
   - Ensure KV namespace is created and bound

3. **Deploy:**
   ```bash
   npm run deploy:cf
   ```

### Vercel Edge Functions

1. **Configure your environment:**
   ```bash
   # Set environment variables in Vercel dashboard or using CLI
   vercel env add GEMINI_API_KEY
   vercel env add PROXY_API_KEYS  # Comma-separated list of valid API keys
   ```

2. **Update `vercel.json`:**
   - Update `CORS_ALLOWED_ORIGINS` to your frontend URL
   - Ensure environment variables are properly configured

3. **Deploy:**
   ```bash
   npm run deploy:vercel
   ```

### Netlify Functions

1. **Configure your environment:**
   ```bash
   # Set environment variables in Netlify dashboard
   # GEMINI_API_KEY
   # PROXY_API_KEYS (comma-separated list of valid API keys)
   ```

2. **Update `netlify.toml`:**
   - Update CORS headers to match your frontend URL

3. **Deploy:**
   ```bash
   # Netlify will automatically deploy when you push to your connected Git repository
   # Or use Netlify CLI: netlify deploy --prod
   ```

## Platform Capabilities

| Feature | Cloudflare Workers | Vercel Edge Functions | Netlify Functions |
|---------|-------------------|----------------------|-------------------|
| **Streaming** | ✅ Full Support | ✅ Full Support | ❌ Not Supported |
| **Non-streaming** | ✅ | ✅ | ✅ |
| **Embeddings** | ✅ | ✅ | ✅ |
| **Caching** | ✅ KV Storage | ✅ In-memory | ✅ In-memory |
| **Cold Start** | ~5ms | ~10-50ms | ~100-500ms |
| **Global Edge** | ✅ 200+ locations | ✅ 100+ locations | ✅ Global CDN |

**Note**: For streaming chat completions, use Cloudflare Workers or Vercel Edge Functions.

## Usage

All three platforms expose OpenAI-compatible API endpoints:

- `GET /models` - List available models
- `POST /chat/completions` - Chat completions (streaming support varies by platform)
- `POST /embeddings` - Text embeddings

### Example Usage:

```javascript
// Using with OpenAI SDK
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: 'your-proxy-api-key',
  baseURL: 'https://your-deployment-url/api/proxy', // Vercel
  // baseURL: 'https://your-worker.your-subdomain.workers.dev', // Cloudflare
  // baseURL: 'https://your-site.netlify.app/.netlify/functions/proxy', // Netlify
});

const completion = await openai.chat.completions.create({
  model: 'gemini-pro',
  messages: [{ role: 'user', content: 'Hello!' }],
  stream: true,
});
```

## Environment Variables

All platforms require these environment variables:

- `GEMINI_API_KEY` - Your Google Gemini API key
- `PROXY_API_KEYS` - Comma-separated list of valid API keys for authentication
- `CORS_ALLOWED_ORIGINS` - Allowed origins for CORS (optional, defaults to *)
- `GEMINI_API_BASE_URL` - Gemini API base URL (optional, defaults to Google's API)
- `GEMINI_API_VERSION` - Gemini API version (optional, defaults to v1beta)
