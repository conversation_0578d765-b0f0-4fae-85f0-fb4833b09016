// tests/netlify/handlers/embeddings.test.js

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { handleEmbeddings } from '../../../src_netlify/handlers/embeddings.js';

// Mock the dependencies
vi.mock('../../../src_netlify/utils/error.js', () => ({
  HttpError: class HttpError extends Error {
    constructor(message, status) {
      super(message);
      this.status = status;
    }
  }
}));

vi.mock('../../../src_netlify/utils/auth.js', () => ({
  createGeminiHeaders: vi.fn(() => ({
    'Content-Type': 'application/json',
    'x-goog-api-key': 'test-key'
  }))
}));

vi.mock('../../../src_netlify/transformers/embeddings.js', () => ({
  transformToGeminiEmbeddingsRequest: vi.fn((req) => ({
    geminiRequest: {
      requests: [{ model: 'models/text-embedding-004', content: { parts: [{ text: req.input }] } }]
    },
    model: 'models/text-embedding-004'
  })),
  transformToOpenAIEmbeddingsResponse: vi.fn((geminiResp, model) => ({
    object: 'list',
    data: [{ object: 'embedding', index: 0, embedding: [0.1, 0.2, 0.3] }],
    model: model,
    usage: { prompt_tokens: 0, total_tokens: 0 }
  }))
}));

// Mock global fetch
global.fetch = vi.fn();

import { createGeminiHeaders } from '../../../src_netlify/utils/auth.js';
import { 
  transformToGeminiEmbeddingsRequest,
  transformToOpenAIEmbeddingsResponse
} from '../../../src_netlify/transformers/embeddings.js';

describe('Netlify Embeddings Handler', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    process.env.GEMINI_API_BASE_URL = 'https://generativelanguage.googleapis.com';
    process.env.GEMINI_API_VERSION = 'v1beta';
  });

  it('should handle embeddings requests successfully', async () => {
    const mockGeminiResponse = {
      embeddings: [
        { values: [0.1, 0.2, 0.3, 0.4, 0.5] }
      ]
    };

    fetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockGeminiResponse)
    });

    const event = {
      body: JSON.stringify({
        model: 'text-embedding-004',
        input: 'Hello world'
      })
    };

    const response = await handleEmbeddings(event);
    
    expect(response.statusCode).toBe(200);
    expect(response.headers['Content-Type']).toBe('application/json');
    
    const body = JSON.parse(response.body);
    expect(body.object).toBe('list');
    expect(body.data[0].object).toBe('embedding');
    expect(transformToGeminiEmbeddingsRequest).toHaveBeenCalledWith({
      model: 'text-embedding-004',
      input: 'Hello world'
    });
    expect(transformToOpenAIEmbeddingsResponse).toHaveBeenCalledWith(
      mockGeminiResponse,
      'text-embedding-004'
    );
  });

  it('should handle invalid JSON in request body', async () => {
    const event = {
      body: 'invalid json'
    };

    await expect(handleEmbeddings(event)).rejects.toThrow('Invalid JSON in request body');
  });

  it('should handle upstream API errors', async () => {
    fetch.mockResolvedValue({
      ok: false,
      status: 400,
      text: () => Promise.resolve('Bad Request')
    });

    const event = {
      body: JSON.stringify({
        model: 'text-embedding-004',
        input: 'Hello world'
      })
    };

    await expect(handleEmbeddings(event)).rejects.toThrow('Upstream API error: 400');
  });

  it('should use environment variable defaults', async () => {
    delete process.env.GEMINI_API_BASE_URL;
    delete process.env.GEMINI_API_VERSION;

    fetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ embeddings: [{ values: [0.1] }] })
    });

    const event = {
      body: JSON.stringify({
        model: 'text-embedding-004',
        input: 'Hello world'
      })
    };

    await handleEmbeddings(event);
    
    expect(fetch).toHaveBeenCalledWith(
      'https://generativelanguage.googleapis.com/v1beta/models/text-embedding-004:batchEmbedContents',
      expect.any(Object)
    );
  });

  it('should call createGeminiHeaders', async () => {
    fetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ embeddings: [{ values: [0.1] }] })
    });

    const event = {
      body: JSON.stringify({
        model: 'text-embedding-004',
        input: 'Hello world'
      })
    };

    await handleEmbeddings(event);
    
    expect(createGeminiHeaders).toHaveBeenCalled();
  });
});
