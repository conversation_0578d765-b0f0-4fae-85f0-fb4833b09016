// vercel/handlers/completions.js

import { HttpError } from '../utils/error.js';
import { createGeminiHeaders } from '../utils/auth.js';
import { transformToGeminiRequest, GeminiToOpenAIStream } from '../transformers/completions.js';

// A helper function to determine the correct Gemini model endpoint.
// This is a simplified version; a real one might have more complex mapping.
function resolveModelName(openAIModel) {
  // Add more sophisticated mapping here if needed.
  // For now, we assume the model name is directly compatible.
  return openAIModel;
}

/**
 * Handles chat completion requests, including streaming.
 * It orchestrates the request transformation, upstream API call,
 * and stream transformation.
 */
export async function handleCompletions(request, env) {
  let openAIRequest;
  try {
    openAIRequest = await request.json();
  } catch (err) {
    throw new HttpError('Invalid JSON in request body', 400);
  }

  // 1. Transform the incoming OpenAI request to the Gemini format.
  const geminiRequest = transformToGeminiRequest(openAIRequest);
  const model = resolveModelName(openAIRequest.model);

  // 2. Determine if the request is for streaming or non-streaming.
  const isStreaming = openAIRequest.stream || false;
  const endpoint = isStreaming ? 'streamGenerateContent' : 'generateContent';
  
  const url = `${env.GEMINI_API_BASE_URL}/${env.GEMINI_API_VERSION}/models/${model}:${endpoint}`;
  
  // For streaming requests, we add the `alt=sse` parameter.
  const finalUrl = isStreaming ? `${url}?alt=sse` : url;

  // 3. Make the fetch call to the Gemini API.
  const headers = createGeminiHeaders(env);
  const upstreamResponse = await fetch(finalUrl, {
    method: 'POST',
    headers: headers,
    body: JSON.stringify(geminiRequest),
  });

  if (!upstreamResponse.ok) {
    const errorBody = await upstreamResponse.text();
    console.error('Upstream API Error:', errorBody);
    throw new HttpError(`Upstream API error: ${upstreamResponse.status} ${errorBody}`, upstreamResponse.status);
  }
  
  // 4. Handle the response based on whether it's streaming or not.
  if (isStreaming) {
    // If streaming, pipe the response through our transformer.
    const encoderStream = new TextEncoderStream();

    const transformedStream = upstreamResponse.body
      .pipeThrough(new GeminiToOpenAIStream(model))
      .pipeThrough(encoderStream); // This pipe ensures the output is binary.

    // Return the transformed stream directly to the client.
    return new Response(transformedStream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    });
  } else {
    // For non-streaming, we need to handle the response differently.
    // This part is not fully implemented yet, as the focus is on streaming.
    // A full implementation would transform the single Gemini response object
    // into a single OpenAI chat completion object.
    const geminiResponse = await upstreamResponse.json();
    
    // Placeholder for non-streaming transformation logic.
    // const openAIResponse = transformGeminiToOpenAIResponse(geminiResponse);
    
    return new Response(JSON.stringify(geminiResponse), { // Returning raw for now
        headers: { 'Content-Type': 'application/json' },
    });
  }
}
