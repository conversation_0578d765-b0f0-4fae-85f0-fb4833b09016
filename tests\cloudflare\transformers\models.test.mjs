// tests/cloudflare/transformers/models.test.mjs

import { describe, it, expect } from 'vitest';
import { transformToOpenAIModels } from '../../../cloudflare/transformers/models.mjs';

// This is our main test suite for this module.
describe('transformToOpenAIModels', () => {

  // This is our first test case.
  it('should transform a Gemini models list to the OpenAI format', () => {
    // 1. ARRANGE: Set up your test data.
    const geminiData = {
      models: [
        {
          name: 'models/gemini-1.5-pro-latest',
          displayName: 'Gemini 1.5 Pro',
          description: 'The most capable model for general purpose tasks.',
        },
        {
          name: 'models/gemini-1.0-pro',
          displayName: 'Gemini 1.0 Pro',
          description: 'A powerful, earlier model.',
        },
      ],
    };

    // 2. ACT: Call the function you are testing.
    const result = transformToOpenAIModels(geminiData);

    // 3. ASSERT: Check if the result is what you expect.
    expect(result).toBeDefined();
    expect(result.object).toBe('list');
    expect(result.data).toHaveLength(2);

    // Check the first model in detail
    const firstModel = result.data[0];
    expect(firstModel.id).toBe('gemini-1.5-pro-latest');
    expect(firstModel.object).toBe('model');
    expect(firstModel.owned_by).toBe('google');
    expect(firstModel.description).toBe('The most capable model for general purpose tasks.');
  });

  it('should handle an empty models list gracefully', () => {
    // ARRANGE: Test an edge case.
    const geminiData = { models: [] };

    // ACT
    const result = transformToOpenAIModels(geminiData);

    // ASSERT
    expect(result.object).toBe('list');
    expect(result.data).toHaveLength(0);
  });
});