/**
 * A custom error class that includes an HTTP status code.
 */
export class HttpError extends Error {
  constructor(message, status) {
    super(message);
    this.name = 'HttpError';
    this.status = status;
  }
}

/**
 * Creates a standardized, safe JSON error Response.
 * It logs the full error for debugging but returns a generic message
 * to the client for server-side errors (status >= 500).
 * @param {Error} err The error object.
 * @returns {Response} A Response object.
 */
export function createErrorResponse(err) {
  const status = err instanceof HttpError ? err.status : 500;

  // Log the detailed error for your own debugging purposes.
  console.error(`Error (status ${status}): ${err.message}`, err.stack);

  // Create a safe response body that doesn't leak implementation details.
  const errorBody = {
    error: {
      message: status >= 500 ? 'An internal server error occurred.' : err.message,
      type: err.name,
      code: status,
    },
  };

  return new Response(JSON.stringify(errorBody), {
    status,
    headers: { 'Content-Type': 'application/json' },
  });
}
