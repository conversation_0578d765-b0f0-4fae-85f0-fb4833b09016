// tests/netlify/transformers/completions.test.js

import { describe, it, expect } from 'vitest';
import { transformToGeminiRequest } from '../../../src_netlify/transformers/completions.js';

describe('Netlify Completions Transformer', () => {
  it('should transform basic OpenAI request to Gemini format', () => {
    const openAIRequest = {
      model: 'gemini-pro',
      messages: [
        { role: 'user', content: 'Hello' }
      ],
      temperature: 0.7,
      max_tokens: 100
    };

    const result = transformToGeminiRequest(openAIRequest);

    expect(result.contents).toHaveLength(1);
    expect(result.contents[0].role).toBe('user');
    expect(result.contents[0].parts[0].text).toBe('Hello');
    expect(result.generationConfig.temperature).toBe(0.7);
    expect(result.generationConfig.maxOutputTokens).toBe(100);
  });

  it('should handle system messages correctly', () => {
    const openAIRequest = {
      model: 'gemini-pro',
      messages: [
        { role: 'system', content: 'You are a helpful assistant.' },
        { role: 'user', content: 'Hello' }
      ]
    };

    const result = transformToGeminiRequest(openAIRequest);

    expect(result.system_instruction).toBeDefined();
    expect(result.system_instruction.parts[0].text).toBe('You are a helpful assistant.');
    expect(result.contents).toHaveLength(1); // System message not in contents
    expect(result.contents[0].role).toBe('user');
  });

  it('should transform all generation config parameters', () => {
    const openAIRequest = {
      model: 'gemini-pro',
      messages: [{ role: 'user', content: 'Hello' }],
      temperature: 0.8,
      top_p: 0.9,
      top_k: 40,
      max_tokens: 500,
      n: 3,
      stop: ['END', 'STOP']
    };

    const result = transformToGeminiRequest(openAIRequest);

    expect(result.generationConfig.temperature).toBe(0.8);
    expect(result.generationConfig.topP).toBe(0.9);
    expect(result.generationConfig.topK).toBe(40);
    expect(result.generationConfig.maxOutputTokens).toBe(500);
    expect(result.generationConfig.candidateCount).toBe(3);
    expect(result.generationConfig.stopSequences).toEqual(['END', 'STOP']);
  });

  it('should handle tool definitions', () => {
    const openAIRequest = {
      model: 'gemini-pro',
      messages: [{ role: 'user', content: 'What\'s the weather?' }],
      tools: [{
        type: 'function',
        function: {
          name: 'get_weather',
          description: 'Get weather information',
          parameters: {
            type: 'object',
            properties: {
              location: { type: 'string' }
            },
            additionalProperties: false
          }
        }
      }]
    };

    const result = transformToGeminiRequest(openAIRequest);

    expect(result.tools).toHaveLength(1);
    expect(result.tools[0].functionDeclarations[0].name).toBe('get_weather');
    expect(result.tools[0].functionDeclarations[0].description).toBe('Get weather information');
    // additionalProperties should be removed
    expect(result.tools[0].functionDeclarations[0].parameters.additionalProperties).toBeUndefined();
  });

  it('should handle tool choice configuration', () => {
    const openAIRequest = {
      model: 'gemini-pro',
      messages: [{ role: 'user', content: 'Hello' }],
      tools: [{ type: 'function', function: { name: 'test_tool' } }],
      tool_choice: 'none'
    };

    const result = transformToGeminiRequest(openAIRequest);

    expect(result.toolConfig).toBeDefined();
    expect(result.toolConfig.functionCallingConfig.mode).toBe('NONE');
  });

  it('should include safety settings', () => {
    const openAIRequest = {
      model: 'gemini-pro',
      messages: [{ role: 'user', content: 'Hello' }]
    };

    const result = transformToGeminiRequest(openAIRequest);

    expect(result.safetySettings).toHaveLength(4);
    expect(result.safetySettings[0].category).toBe('HARM_CATEGORY_HATE_SPEECH');
    expect(result.safetySettings[0].threshold).toBe('BLOCK_NONE');
  });

  it('should handle assistant messages with tool calls', () => {
    const openAIRequest = {
      model: 'gemini-pro',
      messages: [
        { role: 'user', content: 'What\'s the weather?' },
        { 
          role: 'assistant', 
          content: null,
          tool_calls: [{
            id: 'call_123',
            type: 'function',
            function: {
              name: 'get_weather',
              arguments: '{"location": "NYC"}'
            }
          }]
        }
      ]
    };

    const result = transformToGeminiRequest(openAIRequest);

    expect(result.contents).toHaveLength(2);
    expect(result.contents[1].role).toBe('model');
    expect(result.contents[1].parts[0].functionCall.name).toBe('get_weather');
    expect(result.contents[1].parts[0].functionCall.args.location).toBe('NYC');
  });

  it('should handle tool response messages', () => {
    const openAIRequest = {
      model: 'gemini-pro',
      messages: [
        { role: 'user', content: 'What\'s the weather?' },
        { 
          role: 'tool',
          tool_call_id: 'call_123',
          content: 'The weather is sunny, 75°F'
        }
      ]
    };

    const result = transformToGeminiRequest(openAIRequest);

    expect(result.contents).toHaveLength(2);
    expect(result.contents[1].role).toBe('user');
    expect(result.contents[1].parts[0].functionResponse.name).toBe('call_123');
    expect(result.contents[1].parts[0].functionResponse.response.content).toBe('The weather is sunny, 75°F');
  });

  it('should handle mixed content types', () => {
    const openAIRequest = {
      model: 'gemini-pro',
      messages: [
        { role: 'user', content: 'Hello' },
        { role: 'assistant', content: 'Hi there!' },
        { role: 'user', content: 'How are you?' }
      ]
    };

    const result = transformToGeminiRequest(openAIRequest);

    expect(result.contents).toHaveLength(3);
    expect(result.contents[0].role).toBe('user');
    expect(result.contents[1].role).toBe('model');
    expect(result.contents[2].role).toBe('user');
  });

  it('should handle undefined optional parameters gracefully', () => {
    const openAIRequest = {
      model: 'gemini-pro',
      messages: [{ role: 'user', content: 'Hello' }]
      // No optional parameters
    };

    const result = transformToGeminiRequest(openAIRequest);

    expect(result.generationConfig.temperature).toBeUndefined();
    expect(result.generationConfig.topP).toBeUndefined();
    expect(result.generationConfig.maxOutputTokens).toBeUndefined();
    expect(result.tools).toBeUndefined();
    expect(result.toolConfig).toBeUndefined();
    expect(result.safetySettings).toHaveLength(4); // Always present
  });
});
