import { HttpError, createCorsHeaders } from '../utils/error.js';
import { createGeminiHeaders } from '../utils/auth.js';
import { transformToGeminiRequest } from '../transformers/completions.js';

/**
 * Handles chat completion requests (non-streaming only for Netlify).
 * Streaming is not supported on Netlify Functions.
 */
export async function handleCompletions(event) {
  // Check request size (Netlify has 6MB limit, but we'll be more conservative)
  const maxRequestSize = 1024 * 1024; // 1MB
  if (event.body && event.body.length > maxRequestSize) {
    throw new HttpError('Request body too large. Maximum size is 1MB.', 413);
  }

  let openAIRequest;
  try {
    openAIRequest = JSON.parse(event.body);
  } catch (err) {
    throw new HttpError('Invalid JSON in request body', 400);
  }

  // Validate required fields
  if (!openAIRequest.model) {
    throw new HttpError('Missing required field: model', 400);
  }

  if (!openAIRequest.messages || !Array.isArray(openAIRequest.messages) || openAIRequest.messages.length === 0) {
    throw new HttpError('Missing or invalid required field: messages', 400);
  }

  // Reject streaming requests with helpful error message
  if (openAIRequest.stream) {
    return {
      statusCode: 400,
      headers: {
        'Content-Type': 'application/json',
        ...createCorsHeaders(),
      },
      body: JSON.stringify({
        error: {
          message: "Streaming is not supported on Netlify Functions. Use Cloudflare Workers or Vercel Edge Functions for streaming support.",
          type: "unsupported_feature",
          code: 400,
          suggestion: "Remove 'stream: true' from your request or use a different deployment platform."
        }
      })
    };
  }

  // Handle non-streaming requests
  const geminiRequest = transformToGeminiRequest(openAIRequest);
  const baseUrl = process.env.GEMINI_API_BASE_URL || 'https://generativelanguage.googleapis.com';
  const version = process.env.GEMINI_API_VERSION || 'v1beta';

  const url = `${baseUrl}/${version}/models/${openAIRequest.model}:generateContent`;
  const headers = createGeminiHeaders();

  const upstreamResponse = await fetch(url, {
    method: 'POST',
    headers,
    body: JSON.stringify(geminiRequest),
  });

  if (!upstreamResponse.ok) {
    const errorBody = await upstreamResponse.text();
    console.error('Upstream API Error:', errorBody);
    throw new HttpError(`Upstream API error: ${upstreamResponse.status} ${errorBody}`, upstreamResponse.status);
  }

  const geminiResponse = await upstreamResponse.json();

  // Check response size to prevent memory issues
  const responseText = JSON.stringify(geminiResponse);
  const maxResponseSize = 5 * 1024 * 1024; // 5MB
  if (responseText.length > maxResponseSize) {
    throw new HttpError('Response too large. Please reduce max_tokens or simplify your request.', 413);
  }

  // Transform to OpenAI format
  const openAIResponse = transformGeminiToOpenAIResponse(geminiResponse, openAIRequest.model);

  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      ...createCorsHeaders(),
    },
    body: JSON.stringify(openAIResponse),
  };
}

/**
 * Transforms a Gemini non-streaming response to OpenAI format
 */
function transformGeminiToOpenAIResponse(geminiResponse, model) {
  const choices = [];

  // Handle multiple candidates if present
  const candidates = geminiResponse.candidates || [];

  for (let i = 0; i < candidates.length; i++) {
    const candidate = candidates[i];
    const message = {
      role: 'assistant',
      content: null,
      tool_calls: null,
    };

    // Process all parts in the candidate's content
    const parts = candidate?.content?.parts || [];
    const textParts = [];
    const toolCalls = [];

    for (const part of parts) {
      if (part.text) {
        textParts.push(part.text);
      }
      if (part.functionCall) {
        // Generate a more unique ID using crypto-like approach
        const randomSuffix = Math.random().toString(36).substring(2, 15);
        const timestamp = Date.now().toString(36);
        const uniqueId = `call_${timestamp}_${randomSuffix}_${i}_${toolCalls.length}`;

        let functionArgs = '{}';
        try {
          functionArgs = JSON.stringify(part.functionCall.args || {});
        } catch (e) {
          console.warn('Failed to stringify function call args:', part.functionCall.args, e);
          functionArgs = '{}';
        }

        toolCalls.push({
          id: uniqueId,
          type: 'function',
          function: {
            name: part.functionCall.name,
            arguments: functionArgs,
          },
        });
      }
    }

    // Efficiently join text parts
    const textContent = textParts.join('');

    // Set content and tool_calls according to OpenAI format
    if (toolCalls.length > 0) {
      // When there are tool calls, content can be null or string
      message.content = textContent || null;
      message.tool_calls = toolCalls;
    } else {
      // When no tool calls, content should be string (empty string if no text)
      message.content = textContent || '';
    }

    choices.push({
      index: i,
      message: message,
      finish_reason: mapFinishReason(candidate?.finishReason),
    });
  }

  // If no candidates, create a default empty choice
  if (choices.length === 0) {
    choices.push({
      index: 0,
      message: {
        role: 'assistant',
        content: '',
      },
      finish_reason: 'stop',
    });
  }

  return {
    id: `chatcmpl-${Date.now()}`,
    object: 'chat.completion',
    created: Math.floor(Date.now() / 1000),
    model: model,
    choices: choices,
    usage: {
      prompt_tokens: geminiResponse.usageMetadata?.promptTokenCount || 0,
      completion_tokens: geminiResponse.usageMetadata?.candidatesTokenCount || 0,
      total_tokens: geminiResponse.usageMetadata?.totalTokenCount || 0,
    },
  };
}

/**
 * Maps Gemini's finish reason to OpenAI's finish reason
 */
function mapFinishReason(reason) {
  switch (reason) {
    case 'STOP':
      return 'stop';
    case 'MAX_TOKENS':
      return 'length';
    case 'SAFETY':
    case 'RECITATION':
      return 'content_filter';
    case 'TOOL_CALL':
      return 'tool_calls';
    default:
      return 'stop';
  }
}