import { HttpError } from '../utils/error.js';
import { createGeminiHeaders } from '../utils/auth.js';
import { transformToGeminiRequest } from '../transformers/completions.js';

/**
 * Handles chat completion requests (non-streaming only for Netlify).
 * Streaming is not supported on Netlify Functions.
 */
export async function handleCompletions(event) {
  let openAIRequest;
  try {
    openAIRequest = JSON.parse(event.body);
  } catch (err) {
    throw new HttpError('Invalid JSON in request body', 400);
  }

  // Reject streaming requests with helpful error message
  if (openAIRequest.stream) {
    return {
      statusCode: 400,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        error: {
          message: "Streaming is not supported on Netlify Functions. Use Cloudflare Workers or Vercel Edge Functions for streaming support.",
          type: "unsupported_feature",
          code: 400,
          suggestion: "Remove 'stream: true' from your request or use a different deployment platform."
        }
      })
    };
  }

  // Handle non-streaming requests
  const geminiRequest = transformToGeminiRequest(openAIRequest);
  const baseUrl = process.env.GEMINI_API_BASE_URL || 'https://generativelanguage.googleapis.com';
  const version = process.env.GEMINI_API_VERSION || 'v1beta';

  const url = `${baseUrl}/${version}/models/${openAIRequest.model}:generateContent`;
  const headers = createGeminiHeaders();

  const upstreamResponse = await fetch(url, {
    method: 'POST',
    headers,
    body: JSON.stringify(geminiRequest),
  });

  if (!upstreamResponse.ok) {
    const errorBody = await upstreamResponse.text();
    console.error('Upstream API Error:', errorBody);
    throw new HttpError(`Upstream API error: ${upstreamResponse.status} ${errorBody}`, upstreamResponse.status);
  }

  const geminiResponse = await upstreamResponse.json();

  // Transform to OpenAI format
  const openAIResponse = transformGeminiToOpenAIResponse(geminiResponse, openAIRequest.model);

  return {
    statusCode: 200,
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(openAIResponse),
  };
}

/**
 * Transforms a Gemini non-streaming response to OpenAI format
 */
function transformGeminiToOpenAIResponse(geminiResponse, model) {
  const candidate = geminiResponse.candidates?.[0];
  const content = candidate?.content?.parts?.[0]?.text || '';

  return {
    id: `chatcmpl-${Date.now()}`,
    object: 'chat.completion',
    created: Math.floor(Date.now() / 1000),
    model: model,
    choices: [{
      index: 0,
      message: {
        role: 'assistant',
        content: content,
      },
      finish_reason: mapFinishReason(candidate?.finishReason),
    }],
    usage: {
      prompt_tokens: geminiResponse.usageMetadata?.promptTokenCount || 0,
      completion_tokens: geminiResponse.usageMetadata?.candidatesTokenCount || 0,
      total_tokens: geminiResponse.usageMetadata?.totalTokenCount || 0,
    },
  };
}

/**
 * Maps Gemini's finish reason to OpenAI's finish reason
 */
function mapFinishReason(reason) {
  switch (reason) {
    case 'STOP':
      return 'stop';
    case 'MAX_TOKENS':
      return 'length';
    case 'SAFETY':
    case 'RECITATION':
      return 'content_filter';
    case 'TOOL_CALL':
      return 'tool_calls';
    default:
      return 'stop';
  }
}