// Use `import` instead of `require`
import { createGeminiHeaders } from '../utils/auth.js';
import { transformToGeminiRequest, GeminiToOpenAIStream } from '../transformers/completions.js';
// We also need the native fetch, which is available globally, so no import needed.

// Use `export` instead of `module.exports`
export async function handleCompletions(event) {
  const openAIRequest = JSON.parse(event.body);
  const { geminiRequest, model } = transformToGeminiRequest(openAIRequest);

  if (openAIRequest.stream) {
    const url = `${process.env.GEMINI_API_BASE_URL}/${process.env.GEMINI_API_VERSION}/models/${model}:streamGenerateContent?alt=sse`;
    const headers = createGeminiHeaders();

    const upstreamResponse = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(geminiRequest),
    });

    // This part remains the same, as it uses Node.js stream API which is fine.
    const transformStream = new GeminiToOpenAIStream(model);
    upstreamResponse.body.pipe(transformStream);
    
    return {
      statusCode: 200,
      headers: { 'Content-Type': 'text/event-stream' },
      body: transformStream,
    };
  }
  
  return { statusCode: 501, body: 'Non-streaming not implemented' };
}