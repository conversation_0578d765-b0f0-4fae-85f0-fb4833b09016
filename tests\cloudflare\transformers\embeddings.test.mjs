// tests/cloudflare/transformers/embeddings.test.mjs

import { describe, it, expect } from 'vitest';
import {
  transformToGeminiEmbeddingsRequest,
  transformToOpenAIEmbeddingsResponse,
} from '../../../cloudflare/transformers/embeddings.mjs';

describe('transformToGeminiEmbeddingsRequest', () => {
  it('should handle a single string input', () => {
    const openAIRequest = { model: 'text-embedding-004', input: 'Hello world' };
    const { geminiRequest } = transformToGeminiEmbeddingsRequest(openAIRequest);
    
    expect(geminiRequest.requests).toHaveLength(1);
    expect(geminiRequest.requests[0].model).toBe('models/text-embedding-004');
    expect(geminiRequest.requests[0].content.parts[0].text).toBe('Hello world');
  });

  it('should handle an array of strings as input', () => {
    const openAIRequest = { model: 'text-embedding-004', input: ['Hello', 'world'] };
    const { geminiRequest } = transformToGeminiEmbeddingsRequest(openAIRequest);

    expect(geminiRequest.requests).toHaveLength(2);
    expect(geminiRequest.requests[1].content.parts[0].text).toBe('world');
  });
});

describe('transformToOpenAIEmbeddingsResponse', () => {
  it('should transform a Gemini response to the OpenAI format', () => {
    const geminiResponse = {
      embeddings: [
        { values: [0.1, 0.2, 0.3] },
        { values: [0.4, 0.5, 0.6] },
      ],
    };
    const model = 'text-embedding-004';
    const result = transformToOpenAIEmbeddingsResponse(geminiResponse, model);

    expect(result.object).toBe('list');
    expect(result.model).toBe(model);
    expect(result.data).toHaveLength(2);
    expect(result.data[0].object).toBe('embedding');
    expect(result.data[0].index).toBe(0);
    expect(result.data[0].embedding).toEqual([0.1, 0.2, 0.3]);
    expect(result.data[1].index).toBe(1);
  });
});